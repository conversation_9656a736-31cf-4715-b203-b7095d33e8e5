### Create deposit reservation
< {%
    import {sign} from "../signature";

    const {signature, nonce} = sign(request)
    request.variables.set("signature", signature);
    request.variables.set("nonce", nonce);
%}

POST {{RestManagementUrl}}/api/v1/reservations
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

{
  "reservationRef": "slawek-street-cash-trade-001",
  "transactionType": "STREET_CASH_TRADE",
  "dateTime": {{$timestamp}}000,
  "baseCurrency": "BTC",
  "currency": "USD",
  "quantity": 0.001,
  "price": 60000,
  "stopPrice": 60100,
  "portfolioId": "slawek2",
  "accountId": "simulator",
  "feePortfolioId": "slawek2",
  "feeAccountId": "simulator",
  "fees": [
    {
      "amount": 10,
      "currency": "USDT",
      "feeType": "EXCHANGE_FEE"
    }
  ]
}
