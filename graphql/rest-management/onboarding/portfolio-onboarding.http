### Portfolio onboarding (batch)
< {%
  import { sign } from "../signature";


  function generatePortfolios(count) {
      const portfolios = [];
      for (let i = 1; i <= count; i++) {
          const paddedNumber = String(i).padStart(4, '0');
          portfolios.push({
              "portfolioId": `test-001-${i}`,
              "portfolioName": `Test-001-${i}`,
              "portfolioType": "VOSTRO",
              "portfolioCurrency": "EUR",
              "grants": [
                  {
                      "scope": "read",
                      "groups": [
                          "trader",
                          "manager"
                      ]
                  }
              ]
          });
      }
      return JSON.stringify(portfolios);
  }

  const payload = generatePortfolios(10);

  const { signature, nonce } = sign(request, payload)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
  request.variables.set("payload", payload);
%}

POST {{RestManagementUrl}}/api/v1/portfolios/onboarding?ensureUniquePortfolioNames=true
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

{{payload}}

### Portfolio onboarding (single)
< {%
  import { sign } from "../signature";

  const { signature, nonce } = sign(request)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
%}

POST {{RestManagementUrl}}/api/v1/portfolios/onboarding?ensureUniquePortfolioNames=true
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

[
  {
    "portfolioName": "Slawek-20",
    "portfolioType": "NOSTRO",
    "portfolioCurrency": "EUR",
    "grants": [
      {
        "scope": "read",
        "groups": [
          "trader",
          "manager"
        ]
      },
      {
        "scope": "trade",
        "groups": [
          "trader"
        ]
      },
      {
        "scope": "manage",
        "groups": [
          "trader",
          "manager"
        ]
      }
    ]
  }
]
