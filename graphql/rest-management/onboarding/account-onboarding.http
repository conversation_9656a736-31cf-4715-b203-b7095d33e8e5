### Account onboarding
< {%
  import { sign } from "../signature";

  const { signature, nonce } = sign(request)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
%}

POST {{RestManagementUrl}}/api/v1/accounts/onboarding
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

[
  {
    "name": "Slawek-wallet-1",
    "grants": [
      {
        "scope": "read",
        "groups": ["trader", "manager"]
      },
      {
        "scope": "trade",
        "groups": ["trader"]
      },
      {
        "scope": "manage",
        "groups": ["manager"]
      }
    ]
  }
]
