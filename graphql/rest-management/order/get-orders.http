### Get orders
< {%
  import { sign } from "../signature";

  const { signature, nonce } = sign(request)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
%}

GET {{RestManagementUrl}}/api/v1/orders?
    first=20
#    orderId=37a56658-a161-487d-a4bb-2ee069fb2650&
#    clOrderId=a62ea2c5-bf04-4577-aeca-bd38ca3f38c5&
#    portfolioId=testportfolio2&
#    portfolioName=Adam_test&
#    from=1720810092405&
#    to=1720863308189&
#    after=1720799075067&
Accept: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}