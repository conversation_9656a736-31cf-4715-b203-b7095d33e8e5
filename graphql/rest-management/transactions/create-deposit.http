### Create deposit
< {%
  import { sign } from "../signature";

  const { signature, nonce } = sign(request)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
%}

POST {{RestManagementUrl}}/api/v1/transactions
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

{
  "transactionType": "DEPOSIT",
  "reservationRef": "slawek-deposit-2",
  "executionId": "slawek-deposit-2",
  "dateTime": {{$timestamp}}000,
  "currency": "USDT",
  "quantity": 1000,
  "portfolioId": "Slawek2",
  "accountId": "simulator",
  "feePortfolioId": "Slawek2",
  "feeAccountId": "simulator",
  "description": "desc-1",
  "fees": [
    {
      "amount": 0,
      "currency": "USDT",
      "feeType": "EXCHANGE_FEE"
    }
  ]
}
