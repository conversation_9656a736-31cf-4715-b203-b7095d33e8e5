### Create withdrawal
< {%
  import { sign } from "../signature";

  const { signature, nonce } = sign(request)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
%}

POST {{RestManagementUrl}}/api/v1/transactions
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

{
  "transactionType": "WITHDRAWAL",
  "dateTime": {{$timestamp}}000,
  "currency": "USDT",
  "quantity": 90,
  "portfolioId": "Slawek2",
  "accountId": "simulator",
  "feePortfolioId": "Slawek2",
  "feeAccountId": "simulator",
  "description": "desc-1",
  "fees": [
    {
      "amount": 10,
      "currency": "USDT",
      "feeType": "EXCHANGE_FEE"
    }
  ]
}

### Create withdrawal for reservation
< {%
  import { sign } from "../signature";

  const { signature, nonce } = sign(request)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
%}

POST {{RestManagementUrl}}/api/v1/transactions
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

{
  "transactionType": "WITHDRAWAL",
  "reservationRef": "slawek-withdrawal-1",
  "dateTime": *************,
  "currency": "USDT",
  "quantity": 100,
  "portfolioId": "Slawek2",
  "accountId": "simulator",
  "feePortfolioId": "Slawek2",
  "feeAccountId": "simulator",
  "description": "desc-1",
  "fees": [
    {
      "amount": 10,
      "currency": "USDT",
      "feeType": "EXCHANGE_FEE"
    }
  ]
}
