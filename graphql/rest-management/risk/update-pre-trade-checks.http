### Update pre-trade check (buying power check)
< {%
  import { sign } from "../signature";

  const { signature, nonce } = sign(request)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
%}

PUT {{RestManagementUrl}}/api/v1/risk/pre-trade-checks/73f96018-3ea6-4c38-b416-47fb8124f0a7
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

{
  "level": "WARN",
  "portfolioIds": ["BANK_Portfolio"],
  "portfolioTags": [],
  "configuration": [
    {
      "type": "NUMBER",
      "name": "expectedFee",
      "values": [
        "0.05"
      ]
    },
    {
      "type": "NUMBER",
      "name": "marketVolatilityMargin",
      "values": [
        "0.05"
      ]
    }
  ]
}

### Update pre-trade check (trading block check)
< {%
  import { sign } from "../signature";

  const { signature, nonce } = sign(request)
  request.variables.set("signature", signature);
  request.variables.set("nonce", nonce);
%}

PUT {{RestManagementUrl}}/api/v1/risk/pre-trade-checks/2d518895-cfb0-4cbe-a01f-f47eb6b67029
Content-Type: application/json
X-API-KEY: {{ApiKey}}
X-API-NONCE: {{nonce}}
X-API-SIGNATURE: {{signature}}

{
  "level": "BLOCK",
  "portfolioIds": ["Slawektest3", "Slawektest2"],
  "portfolioTags": [],
  "configuration": []
}