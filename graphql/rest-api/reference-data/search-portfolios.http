### Search portfolios

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

query {
    portfolioSearch(search: {first: 5}) {
        pageInfo {
            endCursor
            hasNextPage
        }
        edges {
            cursor
            node {
                id
                name
                portfolioCurrency
                portfolioType
                scopes
                archivedAt
                createdAt
                tags {
                    key
                    value
                }
            }
        }
    }
}
