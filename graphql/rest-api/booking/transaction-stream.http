### Transactions stream
GRAPHQL {{WsApiUrl}}/graphql/ws
Authorization: Bearer {{$auth.token("admin")}}

subscription transactionsWithSnapshot {
     transactionsWithSnapshot(search: {first: 2, currency: ["BNB"]}) {
         __typename
         ... on StreetCashTrade {
             orderId
             quantity
             baseCurrency
             currency
             portfolio
             venueAccount
         }
         ... on ClientCashTrade {
             orderId
             quantity
             baseCurrency
             currency
             portfolio
             counterPortfolio
         }
     }
}
