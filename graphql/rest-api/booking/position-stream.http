### Positions stream
GRAPHQL {{WsApiUrl}}/graphql/ws
Authorization: Bearer {{$auth.token("admin")}}

subscription PositionChanges {
    positionChanges(search: {symbol: ["BTC"]}) {
        updatedAt
        quantity
        currency
        netRealizedPnl
        grossRealizedPnl
        netCost
        grossCost
        reference
        symbol
        notionalQuantity
        marketValue
        marketValueSc
        netRealizedPnlSc
        netCostSc
        grossRealizedPnlSc
        grossCostSc
        netAveragePrice
        grossAveragePrice
        netUnrealizedPnl
        grossUnrealizedPnl
        netUnrealizedPnlSc
        grossUnrealizedPnlSc
    }
}
