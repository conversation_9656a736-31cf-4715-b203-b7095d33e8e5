### Search positions

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

query positions{
    positions(search: {symbol: [], first: 10}) {
        pageInfo {
            hasNextPage
            endCursor
        }
        edges {
            cursor
            node {
                symbol
                currency
                quantity
                account
                accountName
                portfolio {
                    id
                    name
                }
            }
        }
    }
}
