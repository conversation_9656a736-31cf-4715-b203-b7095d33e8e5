### Add deposit

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

mutation addDeposit {
    addTransaction(input: {
        type: DEPOSIT
        dateTime: "2024-11-01T20:20:50.225611Z"
        portfolioId: "slawek2"
        venueAccountId: "simulator"
        fee: "10"
        feePortfolioId: "slawek2"
        feeAccountId: "simulator"
        instrumentId: "ZENBTC@FOREX@Generic"
        executionId: "execution-id"
        venueExecutionId: "venue-execution-id"
        currency: "BTC"
        conversionRates: [{
            baseCurrency: "ZEN"
            quoteCurrency: "USD"
            rate: "12"
        }]
        quantity: "2"
        isSettled: true,
        settlementDate: "2024-01-01T20:20:50.225611Z"
    }) {
        __typename
        status
    }
}

### Add withdrawal

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

mutation addDeposit {
    addTransaction(input: {
        type: WITHDRAWAL
        dateTime: "2024-11-01T20:20:50.225611Z"
        portfolioId: "slawek2"
        venueAccountId: "simulator"
        fee: "10"
        feePortfolioId: "slawek2"
        feeAccountId: "simulator"
        currency: "BTC"
        conversionRatePortfolio: {
            baseCurrency: "BTC"
            quoteCurrency: "EUR"
            rate: "54000"
        }
        quantity: "2"
        isSettled: false,
        executionId: "abc-1"
    }) {
        __typename
        status
    }
}

### Add client-side trade

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

mutation addClientSideTrade {
    addTransaction(input: {
        type: TRADE
        dateTime: "2024-11-01T20:20:50.225611Z"
        portfolioId: "slawek2"
        counterPortfolioId: "slawek3"
        fee: "10"
        feePortfolioId: "slawek2"
        feeAccountId: "simulator"
        instrumentId: "BMEXUSDT@FOREX@Bank"
        conversionRatePortfolio: {
            baseCurrency: "BMEX"
            quoteCurrency: "EUR"
            rate: "12"
        }
        conversionRateCounterPortfolio: {
            baseCurrency: "USDT"
            quoteCurrency: "EUR"
            rate: "0.89"
        }
        quantity: "2"
        price: "52000"
        isSettled: false,
        settlementDate: "2024-01-01T20:20:50.225611Z"
        executionId: "abc-1"
    }) {
        __typename
        status
    }
}

### Add street-side trade

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

mutation addStreetSideTrade {
    addTransaction(input: {
        type: TRADE
        dateTime: "2024-11-01T20:20:50.225611Z"
        portfolioId: "slawek2"
        counterPortfolioId: "slawek2"
        fee: "10"
        feePortfolioId: "slawek2"
        feeAccountId: "simulator"
        instrumentId: "BMEXUSDT@FOREX@Bank"
        conversionRatePortfolio: {
            baseCurrency: "BMEX"
            quoteCurrency: "EUR"
            rate: "12"
        }
        conversionRateCounterPortfolio: {
            baseCurrency: "USDT"
            quoteCurrency: "EUR"
            rate: "0.89"
        }
        quantity: "2"
        price: "52000"
        isSettled: false,
        settlementDate: "2024-01-01T20:20:50.225611Z"
        executionId: "abc-1"
    }) {
        __typename
        status
    }
}