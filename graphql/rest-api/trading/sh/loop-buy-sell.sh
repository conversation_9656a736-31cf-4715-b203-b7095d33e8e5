#!/bin/bash

# Configuration
GRAPHQL_ENDPOINT="https://wyden-dev-api.wyden.io/graphql"
AUTH_TOKEN=""  # Replace with the actual token
COUNTER=35000       # Starting number for incrementing suffix
INTERVAL=1          # Interval in seconds
LIMIT_PRICE=1111    # Shared limit price for both BUY and SELL orders
NUM_ORDERS=5        # Number of BUY and SELL orders per interval

# Function to generate a new clOrderId
generate_clOrderId() {
    local order_type=$1  # BUY or SELL
    echo "${order_type}_$(date +%s%N | cut -c-6)_$COUNTER"
}

# Function to send a GraphQL request
send_order() {
    local cl_order_id=$1
    local portfolio_id=$2
    local side=$3  # BUY or SELL

    # GraphQL mutation
    local graphql_mutation=$(jq -n --arg clOrderId "$cl_order_id" \
                                  --arg instrumentId "DOGEUSD@FOREX@Bank" \
                                  --arg orderType "LIMIT" \
                                  --argjson limitPrice $LIMIT_PRICE \
                                  --arg portfolioId "$portfolio_id" \
                                  --argjson quantity 1 \
                                  --arg side "$side" \
                                  --arg tif "GTC" \
                                  '{
        query: "mutation SendOrder($request: NewOrderSingleRequestInput!) {
            sendOrder(request: $request) {
                clientId
            }
        }",
        variables: {
            request: {
                clOrderId: $clOrderId,
                instrumentId: $instrumentId,
                orderType: $orderType,
                limitPrice: $limitPrice,
                stopPrice: null,
                portfolioId: $portfolioId,
                quantity: $quantity,
                side: $side,
                tif: $tif
            }
        }
    }')

    # Send the request
    response=$(curl -s -X POST "$GRAPHQL_ENDPOINT" \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "$graphql_mutation")

    # Log the response
    echo "Request sent with clOrderId: $cl_order_id (Side: $side)"
    echo "Response: $response"
}

# Main loop
while true; do
    echo "Iteration: $COUNTER"

    # Portfolio IDs
    BUY_PORTFOLIO_ID="769e642a-8ced-4065-bbba-7028422b4110" # maciek-vostro
    SELL_PORTFOLIO_ID="1e768704-2d7c-4859-9414-cca16ebf07e5" # maciek-vostro2

    # Send NUM_ORDERS BUY and SELL orders
    for ((i=0; i<$NUM_ORDERS; i++)); do
        # Generate IDs for BUY and SELL orders
        BUY_ORDER_ID=$(generate_clOrderId "BUY")
        SELL_ORDER_ID=$(generate_clOrderId "SELL")

        # Send BUY and SELL orders
        send_order "$BUY_ORDER_ID" "$BUY_PORTFOLIO_ID" "BUY"
        send_order "$SELL_ORDER_ID" "$SELL_PORTFOLIO_ID" "SELL"

        # Increment the counter
        ((COUNTER++))
    done

    # Wait before the next iteration
    sleep $INTERVAL

done