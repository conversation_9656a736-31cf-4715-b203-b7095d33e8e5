### Search portfolio configuration by portfolio id

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

query portfolioConfiguration {
    portfolioConfiguration(id: "Client 1") {
        id
        name
        executionConfiguration {
            tradingMode
            counterPortfolio
            fixedFee
            percentageFee
            minFee
            feeCurrency
            feeCurrencyType
            agencyTradingAccount
            agencyTargetInstrument {
                baseInstrument {
                    symbol
                    assetClass
                    venueName
                    venueType
                }
            }
            chargeExchangeFee
            discloseTradingVenue
        }
        pricingConfiguration {
            venueAccount
            markup
        }
        hedgingConfiguration {
            autoHedging
            autoHedgingMode
            targetAccount
            thresholdConfiguration {
                asset
                hedgeInstrument
                highThreshold
                lowThreshold
                targetExposure
            }
        }
        instrumentConfiguration {
            instrumentId
            tradeable
            pricingConfiguration {
                pricingSource {
                    venueAccount
                    instrument {
                        baseInstrument {
                            symbol
                            assetClass
                            venueName
                            venueType
                        }
                    }
                }
                markup
            }
            executionConfiguration {
                tradingMode
                counterPortfolio
                fixedFee
                percentageFee
                minFee
                feeCurrency
                feeCurrencyType
                agencyTradingAccount
                agencyTargetInstrument {
                    baseInstrument {
                        symbol
                        assetClass
                        venueName
                        venueType
                    }
                }
                chargeExchangeFee
                discloseTradingVenue
            }
            instrumentGroupConfiguration {
                instrumentId
                tradeable
                executionConfiguration {
                    tradingMode
                    counterPortfolio
                    fixedFee
                    percentageFee
                    minFee
                    feeCurrency
                    feeCurrencyType
                    agencyTradingAccount
                    agencyTargetInstrument {
                        baseInstrument {
                            symbol
                            assetClass
                            venueName
                            venueType
                        }
                    }
                    chargeExchangeFee
                    discloseTradingVenue
                }
                pricingConfiguration {
                    pricingSource {
                        venueAccount
                        instrument {
                            baseInstrument {
                                symbol
                                assetClass
                                venueName
                                venueType
                            }
                        }
                    }
                    markup
                }
            }
        }
        portfolioGroupConfiguration {
            id
            name
            portfolioType
            executionConfiguration {
                tradingMode
                counterPortfolio
                fixedFee
                percentageFee
                minFee
                feeCurrency
                feeCurrencyType
                agencyTradingAccount
                agencyTargetInstrument {
                    baseInstrument {
                        symbol
                        assetClass
                        venueName
                        venueType
                    }
                }
                chargeExchangeFee
                discloseTradingVenue
            }
            pricingConfiguration {
                venueAccount
                markup
            }
            hedgingConfiguration {
                autoHedging
                autoHedgingMode
                targetAccount
                thresholdConfiguration {
                    asset
                    hedgeInstrument
                    highThreshold
                    lowThreshold
                    targetExposure
                }
            }
            instrumentConfiguration {
                instrumentId
                tradeable
                pricingConfiguration {
                    pricingSource {
                        venueAccount
                        instrument {
                            baseInstrument {
                                symbol
                                assetClass
                                venueName
                                venueType
                            }
                        }
                    }
                    markup
                }
                executionConfiguration {
                    tradingMode
                    counterPortfolio
                    fixedFee
                    percentageFee
                    minFee
                    feeCurrency
                    feeCurrencyType
                    agencyTradingAccount
                    agencyTargetInstrument {
                        baseInstrument {
                            symbol
                            assetClass
                            venueName
                            venueType
                        }
                    }
                    chargeExchangeFee
                    discloseTradingVenue
                }
            }
        }
    }
}

### Search portfolio instrument configuration by portfolio id

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

query portfolioConfiguration {
    portfolioConfiguration(id: "Client 1") {
        id
        name
        portfolioGroupConfiguration {
            id
        }
        instrumentConfiguration {
            instrumentId
            tradeable
            instrumentGroupConfiguration {
                instrumentId
                tradeable
            }
        }
    }
}
