### Update portfolio config

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

mutation updatePortfolioConfiguration {
    updatePortfolioConfiguration(
        portfolioId: "Client 1",
        request: {
            id: "Client 1"
            name: "Client 1"
            portfolioGroupConfigurationId: "Crypto Group"
            executionConfiguration: {
                tradingMode: PRINCIPAL
                counterPortfolio: "Bank"
                fixedFee: 10
                percentageFee: 0.5
                minFee: 10
                feeCurrency: "USD"
                feeCurrencyType: SPECIFIC_CURRENCY
                agencyTradingAccount: "BITMEX-testnet-1"
                agencyTargetInstrumentId: "DOGEUSD@FOREX@Bank"
                chargeExchangeFee: true
                discloseTradingVenue: true
            }
            pricingConfiguration: {
                markup: 2.99999
                venueAccount: ["bitmex-testnet1", "Simulator"]
            }
            hedgingConfiguration: {
                autoHedging: true,
                autoHedgingMode: ONE_TO_ONE,
                targetAccount: "Bank",
                thresholdConfiguration: [{
                    asset: "BTC",
                    hedgeInstrument: "DOGEUSD@FOREX@Bank",
                    highThreshold: 100,
                    lowThreshold: -100,
                    targetExposure: 0
                }, {
                    asset: "ETH",
                    hedgeInstrument: "ETHUSD@FOREX@Coinbase",
                    highThreshold: 10000,
                    lowThreshold: -10000,
                    targetExposure: 0
                }]
            }
            instrumentConfiguration: [{
                instrumentId: "DOGEUSD@FOREX@Bank"
                tradeable: true
                executionConfiguration: {
                    tradingMode: AGENCY
                    counterPortfolio: "Bank"
                    fixedFee: 10
                    percentageFee: 0.5
                    minFee: 10
                    feeCurrency: "USD"
                    agencyTradingAccount: "BITMEX-testnet-1"
                    agencyTargetInstrumentId: "DOGEUSD@FOREX@Bank"
                    chargeExchangeFee: true
                    discloseTradingVenue: true
                }
                pricingConfiguration: {
                    markup: 0.3
                    pricingSource: [{
                        venueAccount: "bitmex-testnet1",
                        instrumentId: "DOGEUSD@FOREX@Bank"
                    }, {
                        venueAccount: "Simulator",
                        instrumentId: "BTCUSD@FOREX@Simulator"
                    }]
                }
            }]
        })
}
