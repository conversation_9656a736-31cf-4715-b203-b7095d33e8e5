image:
  debug: false
auth:
  username: "whiterabbit"
  password: "G9az+R#a0%9!swMY3"
  erlangCookie: "hiKljo9r1IlJ7T3Uagp9jac0K/TA0CoNRbuGlFaH"

replicaCount: 1

clustering:
  enabled: false

resources:
  requests:
    cpu: 1
    memory: 2Gi
  limits:
    cpu: 2
    memory: 2Gi

extraPlugins: "rabbitmq_consistent_hash_exchange"

extraConfiguration: |-
  prometheus.return_per_object_metrics = true

memoryHighWatermark:
  enabled: true
  type: "relative"
  value: 0.9

metrics:
  enabled: true
  serviceMonitor:
    enabled: true

loadDefinition:
  enabled: true
  existingSecret: load-definition

readinessProbe:
  enabled: true
  initialDelaySeconds: 60

ingress:
  enabled: true
  path: /
  hostname: rabbitmq-garanti-uat.wyden.io
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-wyden"
  tls: true
  ingressClassName: "nginx"

nodeSelector:
  environment: garanti-uat
tolerations:
  - key: "garanti-uat"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
  - key: "kubernetes.azure.com/scalesetpriority"
    operator: "Equal"
    value: "spot"
    effect: "NoSchedule"
