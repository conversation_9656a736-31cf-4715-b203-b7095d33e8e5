global:
  imageRegistry: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub

auth:
  username: "whiterabbit"
  password: "wQt2n7PowwHs"
  erlangCookie: "W37DnTN5sq8iTRdb7gjsF4ECA6PP4mg7"

replicaCount: 3

clustering:
  rebalance: true

resources:
  requests:
    cpu: 1
    memory: 2Gi
  limits:
    cpu: 2
    memory: 2Gi

extraPlugins: "rabbitmq_consistent_hash_exchange"

extraConfiguration: |-
  prometheus.return_per_object_metrics = true

memoryHighWatermark:
  enabled: true
  type: "relative"
  value: 0.9

metrics:
  enabled: true
  serviceMonitor:
    enabled: true

loadDefinition:
  enabled: true
  existingSecret: load-definition

readinessProbe:
  enabled: true
  initialDelaySeconds: 120

ingress:
  enabled: true
  path: /
  hostname: rabbitmq-uat.wyden.io
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-wyden"
  tls: true
  ingressClassName: "nginx"

nodeSelector:
  environment: uat
tolerations:
  - key: "uat"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

affinity: |
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: "{{ .Release.Name }}"
        topologyKey: kubernetes.io/hostname
