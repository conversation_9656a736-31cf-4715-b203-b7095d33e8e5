global:
  imageRegistry: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub

image:
  debug: false
auth:
  username: "whiterabbit"
  password: "G9az+R#a0%9!swMY3"
  erlangCookie: "hiKljo9r1IlJ7T3Uagp9jac0K/TA0CoNRbuGlFaH"

replicaCount: 1

clustering:
  enabled: false

resources:
  requests:
    cpu: 1
    memory: 2Gi
  limits:
    cpu: 2
    memory: 2Gi

extraPlugins: "rabbitmq_consistent_hash_exchange"

extraConfiguration: |-
  prometheus.return_per_object_metrics = true

memoryHighWatermark:
  enabled: true
  type: "relative"
  value: 0.9

metrics:
  enabled: true
  serviceMonitor:
    enabled: true

loadDefinition:
  enabled: true
  existingSecret: load-definition

readinessProbe:
  enabled: true
  initialDelaySeconds: 60

ingress:
  enabled: true
  path: /
  hostname: rabbitmq-garanti-uat.wyden.io
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-wyden"
  tls: true
  ingressClassName: "nginx"

nodeSelector:
  environment: garantiuat
tolerations:
  - key: "garantiuat"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
  - key: "client"
    operator: "Equal"
    value: "garanti"
    effect: "NoSchedule"
