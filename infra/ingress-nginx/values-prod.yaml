controller:
  replicaCount: 2
  service:
    externalTrafficPolicy: Local

  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

  config:
    log-format-escape-json: "true"
    log-format-upstream: '{"time": "$time_local", "remote_addr": "$remote_addr", "request_id": "$req_id", "remote_user": "$remote_user", "host": "$host", "path": "$uri", "method": "$request_method", "request_query": "$args", "status": "$status", "upstream_status": "$upstream_status",  "bytes_sent": "$bytes_sent", "request_time": "$request_time", "request_proto": "$server_protocol", "request_length": "$request_length", "duration": "$request_time", "http_referrer": "$http_referer", "http_user_agent": "$http_user_agent" }'
