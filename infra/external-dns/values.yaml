# Copyright Broadcom, Inc. All Rights Reserved.
# SPDX-License-Identifier: APACHE-2.0

## @section Global parameters
## Global Docker image parameters
## Please, note that this will override the image parameters, including dependencies, configured to use the global value
## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass
##

## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker registry secret names as an array
##
global:
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ##
  imagePullSecrets: []
  ## Compatibility adaptations for Kubernetes platforms
  ##
  compatibility:
    ## Compatibility adaptations for Openshift
    ##
    openshift:
      ## @param global.compatibility.openshift.adaptSecurityContext Adapt the securityContext sections of the deployment to make them compatible with Openshift restricted-v2 SCC: remove runAsUser, runAsGroup and fsGroup and let the platform use their allowed default IDs. Possible values: auto (apply if the detected running cluster is Openshift), force (perform the adaptation always), disabled (do not perform adaptation)
      ##
      adaptSecurityContext: auto
## @section Common parameters
##

## @param nameOverride String to partially override common.names.fullname template (will maintain the release name)
##
nameOverride: ""
## @param fullnameOverride String to fully override common.names.fullname template
##
fullnameOverride: ""
## @param namespaceOverride String to fully override common.names.namespace
##
namespaceOverride: ""
## @param clusterDomain Kubernetes Cluster Domain
##
clusterDomain: cluster.local
## @param commonLabels Labels to add to all deployed objects
##
commonLabels: {}
## @param commonAnnotations Annotations to add to all deployed objects
##
commonAnnotations: {}
##
## @param extraDeploy Array of extra objects to deploy with the release (evaluated as a template).
##
extraDeploy: []
## @param kubeVersion Force target Kubernetes version (using Helm capabilities if not set)
##
kubeVersion: ""

## @section external-dns parameters
##

## Bitnami external-dns image version
## ref: https://hub.docker.com/r/bitnami/external-dns/tags/
## @param image.registry [default: REGISTRY_NAME] ExternalDNS image registry
## @param image.repository [default: REPOSITORY_NAME/external-dns] ExternalDNS image repository
## @skip image.tag ExternalDNS Image tag (immutable tags are recommended)
## @param image.digest ExternalDNS image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
## @param image.pullPolicy ExternalDNS image pull policy
## @param image.pullSecrets ExternalDNS image pull secrets
##
image:
  registry: docker.io
  repository: bitnami/external-dns
  tag: 0.15.0-debian-12-r4
  digest: ""
  ## Specify a imagePullPolicy
  ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
  ##
  pullPolicy: IfNotPresent
  ## Optionally specify an array of imagePullSecrets.
  ## Secrets must be manually created in the namespace.
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
  ## e.g:
  ## pullSecrets:
  ##   - myRegistryKeySecretName
  ##
  pullSecrets: []
## @param revisionHistoryLimit sets number of replicaset to keep in k8s
##
revisionHistoryLimit: 10
## @param automountServiceAccountToken Mount Service Account token in pod
##
automountServiceAccountToken: true
## @param hostAliases Deployment pod host aliases
## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
##
hostAliases: []
## @param updateStrategy update strategy type
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#update-strategies
##
updateStrategy: {}
## @param command Override kiam default command
##
command: []
## @param args Override kiam default args
##
args: []
## @param sources [array] K8s resources type to be observed for new DNS entries by ExternalDNS
##
sources:
  # - crd
  - service
  - ingress
  # - contour-httpproxy
## @param provider DNS provider where the DNS records will be created.
## Available providers are:
## - akamai, alibabacloud, aws, azure, azure-private-dns, cloudflare, coredns, designate, digitalocean, google, hetzner, infoblox, linode, rfc2136, transip, oci
##
provider: aws
## @param initContainers Attach additional init containers to the pod (evaluated as a template)
##
initContainers: []
## DNS-Pod services
## Ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/
## @param dnsPolicy Specifies the DNS policy for the external-dns deployment
## DNS policies can be set on a per-Pod basis. Currently Kubernetes supports the following Pod-specific DNS policies.
## Available options: Default, ClusterFirst, ClusterFirstWithHostNet, None
## Ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#pod-s-dns-policy
dnsPolicy: ""
## @param dnsConfig  allows users more control on the DNS settings for a Pod. Required if `dnsPolicy` is set to `None`
## The dnsConfig field is optional and it can work with any dnsPolicy settings.
## Ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#pod-dns-config
## E.g.
## dnsConfig:
##   nameservers:
##     - ********* # this is an example
##   searches:
##     - ns1.svc.cluster-domain.example
##     - my.dns.search.suffix
##   options:
##     - name: ndots
##       value: "2"
##     - name: edns0
dnsConfig: {}
## @param sidecars Attach additional containers to the pod (evaluated as a template)
##
sidecars: []
## Flags related to processing sources
## ref: https://github.com/kubernetes-sigs/external-dns/blob/master/pkg/apis/externaldns/types.go#L272
## @param namespace Limit sources of endpoints to a specific namespace (default: all namespaces)
##
namespace: ""
## @param watchReleaseNamespace Watch only namespace used for the release
##
watchReleaseNamespace: false
## @param fqdnTemplates Templated strings that are used to generate DNS names from sources that don't define a hostname themselves
##
fqdnTemplates: []
## @param containerPorts.http HTTP Container port
##
containerPorts:
  http: 7979
## @param combineFQDNAnnotation Combine FQDN template and annotations instead of overwriting
##
combineFQDNAnnotation: false
## @param ignoreHostnameAnnotation Ignore hostname annotation when generating DNS names, valid only when fqdn-template is set
##
ignoreHostnameAnnotation: false
## @param publishInternalServices Allow external-dns to publish DNS records for ClusterIP services
##
publishInternalServices: false
## @param publishHostIP Allow external-dns to publish host-ip for headless services
##
publishHostIP: false
## @param serviceTypeFilter The service types to take care about (default: all, options: ClusterIP, NodePort, LoadBalancer, ExternalName)
##
serviceTypeFilter: []
## Chart Validation
##
validation:
  ## @param validation.enabled Enable chart validation
  ##
  enabled: true
## Akamai configuration to be set via arguments/env. variables
##
akamai:
  ## @param akamai.host Hostname to use for EdgeGrid auth
  ##
  host: ""
  ## @param akamai.accessToken Access Token to use for EdgeGrid auth
  ##
  accessToken: ""
  ## @param akamai.clientToken Client Token to use for EdgeGrid auth
  ##
  clientToken: ""
  ## @param akamai.clientSecret When using the Akamai provider, `AKAMAI_CLIENT_SECRET` to set (optional)
  ##
  clientSecret: ""
  ## @param akamai.secretName Use an existing secret with key "akamai_api_seret" defined.
  ## This ignores akamai.clientSecret
  ##
  secretName: ""
## Alibaba cloud configuration to be set via arguments/env. variables
## These will be added to /etc/kubernetes/alibaba-cloud.json via secret
##
alibabacloud:
  ## @param alibabacloud.accessKeyId When using the Alibaba Cloud provider, set `accessKeyId` in the Alibaba Cloud configuration file (optional)
  ##
  accessKeyId: ""
  ## @param alibabacloud.accessKeySecret When using the Alibaba Cloud provider, set `accessKeySecret` in the Alibaba Cloud configuration file (optional)
  ##
  accessKeySecret: ""
  ## @param alibabacloud.regionId When using the Alibaba Cloud provider, set `regionId` in the Alibaba Cloud configuration file (optional)
  ##
  regionId: ""
  ## @param alibabacloud.vpcId Alibaba Cloud VPC Id
  ##
  vpcId: ""
  ## @param alibabacloud.secretName Use an existing secret with key "alibaba-cloud.json" defined.
  ## This ignores alibabacloud.accessKeyId, and alibabacloud.accessKeySecret
  ##
  secretName: ""
  ## @param alibabacloud.zoneType Zone Filter. Available values are: public, private, or no value for both
  ##
  zoneType: ""
## AWS configuration to be set via arguments/env. variables
##
aws:
  ## AWS credentials
  ## @param aws.credentials.secretKey When using the AWS provider, set `aws_secret_access_key` in the AWS credentials (optional)
  ## @param aws.credentials.accessKey When using the AWS provider, set `aws_access_key_id` in the AWS credentials (optional)
  ## @param aws.credentials.mountPath When using the AWS provider, determine `mountPath` for `credentials` secret
  ##
  credentials:
    secretKey: ""
    accessKey: ""
    ## Before external-dns 0.5.9 home dir should be `/root/.aws`
    ##
    mountPath: "/.aws"
    ## @param aws.credentials.secretName Use an existing secret with key "credentials" defined.
    ## This ignores aws.credentials.secretKey, and aws.credentials.accessKey
    ##
    secretName: ""
    ## AWS access key id stored in key-value secret.
    ## If aws.credentials.accessKeyIDSecretRef and aws.credentials.secretAccessKeySecretRef defined aws.credentials.secretKey, aws.credentials.accessKey and aws.credentials.secretName are ignored
    ## @param aws.credentials.accessKeyIDSecretRef.name Define the name of the secret that stores aws_access_key_id.
    ## @param aws.credentials.accessKeyIDSecretRef.key Define the key of the secret that stores aws_access_key_id.
    ##
    accessKeyIDSecretRef:
      name: ""
      key: ""
    ## AWS secret access key stored in key-value secret
    ## @param aws.credentials.secretAccessKeySecretRef.name Define the name of the secret that stores aws_secret_access_key
    ## @param aws.credentials.secretAccessKeySecretRef.key Define the key of the secret that stores aws_secret_access_key
    ##
    secretAccessKeySecretRef:
      name: ""
      key: ""
  ## @param aws.region When using the AWS provider, `AWS_DEFAULT_REGION` to set in the environment (optional)
  ##
  region: "us-east-1"
  ## @param aws.zoneType When using the AWS provider, filter for zones of this type (optional, options: public, private)
  ##
  zoneType: ""
  ## @param aws.assumeRoleArn When using the AWS provider, assume role by specifying --aws-assume-role to the external-dns daemon
  ##
  assumeRoleArn: ""
  ## @param aws.roleArn Specify role ARN to the external-dns daemon
  ##
  roleArn: ""
  ## @param aws.apiRetries Maximum number of retries for AWS API calls before giving up
  ##
  apiRetries: 3
  ## @param aws.batchChangeSize When using the AWS provider, set the maximum number of changes that will be applied in each batch
  ##
  batchChangeSize: 1000
  ## @param aws.zonesCacheDuration If the list of Route53 zones managed by ExternalDNS doesn't change frequently, cache it by setting a TTL
  ## (default 0 - disabled, can be set to time interval like 1m or 1h)
  ##
  zonesCacheDuration: 0
  ## @param aws.zoneTags When using the AWS provider, filter for zones with these tags
  ##
  zoneTags: []
  ## @param aws.preferCNAME When using the AWS provider, replaces Alias records with CNAME (options: true, false)
  ##
  preferCNAME: ""
  ## @param aws.evaluateTargetHealth When using the AWS provider, sets the evaluate target health flag (options: true, false)
  ##
  evaluateTargetHealth: ""
  ## @param aws.dynamodbTable When using the AWS provider, sets the DynamoDB table name to use for dynamodb registry
  ## ref: https://github.com/kubernetes-sigs/external-dns/blob/0483ffde22e60436f16be154b9fe1a388a1400d0/docs/registry/dynamodb.md
  ##
  dynamodbTable: ""
  ## @param aws.dynamodbRegion When using the AWS provider, sets the DynamoDB table region to use for dynamodb registry
  ## ref: https://github.com/kubernetes-sigs/external-dns/blob/0483ffde22e60436f16be154b9fe1a388a1400d0/docs/registry/dynamodb.md
  ##
  dynamodbRegion: ""
  ## @param aws.zoneMatchParent When using the AWS provider, lets a domain filter match subdomains within the same zone by using their parent domain
  ##
  zoneMatchParent: false
## Azure configuration to be set via arguments/env. variables
##
azure:
  ## When a secret to load azure.json is not specified, the host's /etc/kubernetes/azure.json will be used
  ## @param azure.secretName When using the Azure provider, set the secret containing the `azure.json` file
  ##
  secretName: ""
  ## @param azure.cloud When using the Azure provider, set the Azure Cloud
  ##
  cloud: ""
  ## @param azure.resourceGroup When using the Azure provider, set the Azure Resource Group
  ##
  resourceGroup: ""
  ## @param azure.tenantId When using the Azure provider, set the Azure Tenant ID
  ##
  tenantId: ""
  ## @param azure.subscriptionId When using the Azure provider, set the Azure Subscription ID
  ##
  subscriptionId: ""
  ## @param azure.aadClientId When using the Azure provider, set the Azure AAD Client ID
  ##
  aadClientId: ""
  ## @param azure.aadClientSecret When using the Azure provider, set the Azure AAD Client Secret
  ##
  aadClientSecret: ""
  ## @param azure.useWorkloadIdentityExtension When using the Azure provider, set if you use Workload Identity extension.
  ##
  useWorkloadIdentityExtension: false
  ## @param azure.useManagedIdentityExtension When using the Azure provider, set if you use Azure MSI
  ##
  useManagedIdentityExtension: false
  ## @param azure.userAssignedIdentityID When using the Azure provider with Azure MSI, set Client ID of Azure user-assigned managed identity (optional, otherwise system-assigned managed identity is used)
  ##
  userAssignedIdentityID: ""
## Civo configuration to be set via arguments/env. variables
##
civo:
  ## @param civo.apiToken When using the Civo provider, `CIVO_TOKEN` to set (optional)
  ##
  apiToken: ""
  ## @param civo.secretName Use an existing secret with key "apiToken" defined.
  ## This ignores civo.apiToken
  ##
  secretName: ""
## Cloudflare configuration to be set via arguments/env. variables
##
cloudflare:
  ## @param cloudflare.apiToken When using the Cloudflare provider, `CF_API_TOKEN` to set (optional)
  ##
  apiToken: ""
  ## @param cloudflare.apiKey When using the Cloudflare provider, `CF_API_KEY` to set (optional)
  ##
  apiKey: ""
  ## @param cloudflare.secretName When using the Cloudflare provider, it's the name of the secret containing cloudflare_api_token or cloudflare_api_key.
  ## This ignores cloudflare.apiToken, and cloudflare.apiKey
  ##
  secretName: ""
  ## @param cloudflare.email When using the Cloudflare provider, `CF_API_EMAIL` to set (optional). Needed when using CF_API_KEY
  ##
  email: ""
  ## @param cloudflare.proxied When using the Cloudflare provider, enable the proxy feature (DDOS protection, CDN...) (optional)
  ##
  proxied: true
  ## @param cloudflare.dnsRecordsPerPage Number of DNS records to fetch per page. (optional)
  ## When using the Cloudflare provider, specify how many DNS records listed per page, max possible 5,000 (default: 100)
  ##
  dnsRecordsPerPage: 100
## CoreDNS configuration to be set via arguments/env variables
##
coredns:
  ## @param coredns.etcdEndpoints When using the CoreDNS provider, set etcd backend endpoints (comma-separated list)
  ## Secure (https) endpoints can be used as well, in that case `etcdTLS` section
  ## should be filled in accordingly
  ##
  etcdEndpoints: "http://etcd-extdns:2379"
  ## Configuration of the secure communication and client authentication to the etcd cluster
  ## If enabled all the values under this key must hold a valid data
  ##
  etcdTLS:
    ## @param coredns.etcdTLS.enabled When using the CoreDNS provider, enable secure communication with etcd
    ##
    enabled: false
    ## @param coredns.etcdTLS.autoGenerated Generate automatically self-signed TLS certificates
    ##
    autoGenerated: false
    ## @param coredns.etcdTLS.secretName When using the CoreDNS provider, specify a name of existing Secret with etcd certs and keys
    ## ref: https://github.com/etcd-io/etcd/blob/master/Documentation/op-guide/security.md
    ## ref (secret creation):
    ##  https://github.com/bitnami/charts/tree/main/bitnami/etcd#configure-certificates-for-client-communication
    ##
    secretName: "etcd-client-certs"
    ## @param coredns.etcdTLS.mountPath When using the CoreDNS provider, set destination dir to mount data from `coredns.etcdTLS.secretName` to
    ##
    mountPath: "/etc/coredns/tls/etcd"
    ## @param coredns.etcdTLS.caFilename When using the CoreDNS provider, specify CA PEM file name from the `coredns.etcdTLS.secretName`
    ##
    caFilename: "ca.crt"
    ## @param coredns.etcdTLS.certFilename When using the CoreDNS provider, specify cert PEM file name from the `coredns.etcdTLS.secretName`
    ## Will be used by external-dns to authenticate against etcd
    ##
    certFilename: "cert.pem"
    ## @param coredns.etcdTLS.keyFilename When using the CoreDNS provider, specify private key PEM file name from the `coredns.etcdTLS.secretName`
    ## Will be used by external-dns to authenticate against etcd
    ##
    keyFilename: "key.pem"
## OpenStack Designate provider configuration to be set via arguments/env. variables
##
designate:
  ## Set Openstack environment variables (optional). Username and password will be saved in a kubernetes secret
  ## The alternative to this is to export the necessary Openstack environment variables in the extraEnv argument
  ## @param designate.username When using the Designate provider, specify the OpenStack authentication username. (optional)
  ## @param designate.password When using the Designate provider, specify the OpenStack authentication password. (optional)
  ## @param designate.applicationCredentialId When using the Designate provider, specify the OpenStack authentication application credential ID. This conflicts with `designate.username`. (optional)
  ## @param designate.applicationCredentialSecret When using the Designate provider, specify the OpenStack authentication application credential ID. This conflicts with `designate.password`. (optional)
  ## @param designate.authUrl When using the Designate provider, specify the OpenStack authentication Url. (optional)
  ## @param designate.regionName When using the Designate provider, specify the OpenStack region name. (optional)
  ## @param designate.userDomainName When using the Designate provider, specify the OpenStack user domain name. (optional)
  ## @param designate.projectName When using the Designate provider, specify the OpenStack project name. (optional)
  ## @param designate.authType When using the Designate provider, specify the OpenStack auth type. (optional)
  ## e.g:
  ##   username: "someuser"
  ##   password: "p@55w0rd"
  ##   authUrl: "https://mykeystone.example.net:5000/v3/"
  ##   regionName: "dev"
  ##   userDomainName: "development"
  ##   projectName: "myteamname"
  ##
  username: ""
  password: ""
  applicationCredentialId: ""
  applicationCredentialSecret: ""
  authUrl: ""
  regionName: ""
  userDomainName: ""
  projectName: ""
  authType: ""
  ## @param designate.customCAHostPath When using the Designate provider, use a CA file already on the host to validate Openstack APIs.  This conflicts with `designate.customCA.enabled`
  ## This conflicts setting the above customCA to true and chart rendering will fail if you set customCA to true and specify customCAHostPath
  ##
  customCAHostPath: ""
  ## Use a custom CA (optional)
  ## @param designate.customCA.enabled When using the Designate provider, enable a custom CA (optional)
  ## @param designate.customCA.content When using the Designate provider, set the content of the custom CA
  ## @param designate.customCA.mountPath When using the Designate provider, set the mountPath in which to mount the custom CA configuration
  ## @param designate.customCA.filename When using the Designate provider, set the custom CA configuration filename
  ##
  customCA:
    enabled: false
    content: ""
    mountPath: "/config/designate"
    filename: "designate-ca.pem"
## Exoscale configuration to be set via arguments/env. variables
##
exoscale:
  ## @param exoscale.apiKey When using the Exoscale provider, `EXTERNAL_DNS_EXOSCALE_APIKEY` to set (optional)
  ##
  apiKey: ""
  ## @param exoscale.apiToken When using the Exoscale provider, `EXTERNAL_DNS_EXOSCALE_APISECRET` to set (optional)
  ##
  apiToken: ""
  ## @param exoscale.secretName Use an existing secret with keys "exoscale_api_key" and "exoscale_api_token" defined.
  ## This ignores exoscale.apiKey and exoscale.apiToken
  ##
  secretName: ""
## Google configuration to be set via arguments/env. variables
##
## DigitalOcean configuration to be set via arguments/env. variables
##
digitalocean:
  ## @param digitalocean.apiToken When using the DigitalOcean provider, `DO_TOKEN` to set (optional)
  ##
  apiToken: ""
  ## @param digitalocean.secretName Use an existing secret with key "digitalocean_api_token" defined.
  ## This ignores digitalocean.apiToken
  ##
  secretName: ""
## Google configuration to be set via arguments/env. variables
##
google:
  ## @param google.project When using the Google provider, specify the Google project (required when provider=google)
  ##
  project: ""
  ## @param google.batchChangeSize When using the google provider, set the maximum number of changes that will be applied in each batch
  ##
  batchChangeSize: 1000
  ## @param google.serviceAccountSecret When using the Google provider, specify the existing secret which contains credentials.json (optional)
  ##
  serviceAccountSecret: ""
  ## @param google.serviceAccountSecretKey When using the Google provider with an existing secret, specify the key name (optional)
  ##
  serviceAccountSecretKey: "credentials.json"
  ## @param google.serviceAccountKey When using the Google provider, specify the service account key JSON file. In this case a new secret will be created holding this service account (optional)
  ##
  serviceAccountKey: ""
  ## @param google.zoneVisibility When using the Google provider, fiter for zones of a specific visibility (private or public)
  ##
  zoneVisibility: ""
## Hetzner configuration to be set via arguments/env. variables
##
hetzner:
  ## @param hetzner.token When using the Hetzner provider, specify your token here. (required when `hetzner.secretName` is not provided. In this case a new secret will be created holding the token.)
  ## Mutually exclusive with `hetzner.secretName`.
  ##
  token: ""
  ## @param hetzner.secretName When using the Hetzner provider, specify the existing secret which contains your token. Disables the usage of `hetzner.token` (optional)
  ##
  secretName: ""
  ## @param hetzner.secretKey When using the Hetzner provider with an existing secret, specify the key name (optional)
  ##
  secretKey: "hetzner_token"
## Infoblox configuration to be set via arguments/env. variables
##
infoblox:
  ## @param infoblox.wapiUsername When using the Infoblox provider, specify the Infoblox WAPI username
  ##
  wapiUsername: "admin"
  ## @param infoblox.wapiPassword When using the Infoblox provider, specify the Infoblox WAPI password (required when provider=infoblox)
  ##
  wapiPassword: ""
  ## @param infoblox.gridHost When using the Infoblox provider, specify the Infoblox Grid host (required when provider=infoblox)
  ##
  gridHost: ""
  ## @param infoblox.view Infoblox view
  ##
  view: ""
  ## Optional keys
  ##
  ## @param infoblox.secretName Existing secret name, when in place wapiUsername and wapiPassword are not required
  ##
  secretName: ""
  ##
  ## @param infoblox.domainFilter When using the Infoblox provider, specify the domain (optional)
  ##
  domainFilter: ""
  ##
  ## @param infoblox.nameRegex When using the Infoblox provider, specify the name regex filter (optional)
  ##
  nameRegex: ""
  ## @param infoblox.noSslVerify When using the Infoblox provider, disable SSL verification (optional)
  ##
  noSslVerify: false
  ## @param infoblox.wapiPort When using the Infoblox provider, specify the Infoblox WAPI port (optional)
  ##
  wapiPort: ""
  ## @param infoblox.wapiVersion When using the Infoblox provider, specify the Infoblox WAPI version (optional)
  ##
  wapiVersion: ""
  ## @param infoblox.wapiConnectionPoolSize When using the Infoblox provider, specify the Infoblox WAPI request connection pool size (optional)
  ##
  wapiConnectionPoolSize: ""
  ## @param infoblox.wapiHttpTimeout When using the Infoblox provider, specify the Infoblox WAPI request timeout in seconds (optional)
  ##
  wapiHttpTimeout: ""
  ## @param infoblox.maxResults When using the Infoblox provider, specify the Infoblox Max Results (optional)
  ##
  maxResults: ""
  ## @param infoblox.createPtr When using the Infoblox provider, specify the Infoblox create PTR flag (optional)
  ##
  createPtr: false
## Linode configuration to be set via arguments/env. variables
##
linode:
  ## @param linode.apiToken When using the Linode provider, `LINODE_TOKEN` to set (optional)
  ##
  apiToken: ""
  ## @param linode.secretName Use an existing secret with key "linode_api_token" defined.
  ## This ignores linode.apiToken
  ##
  secretName: ""
## NS1 configuration to be set via arguments/env. variables
##
ns1:
  ## @param ns1.minTTL When using the ns1 provider, specify minimal TTL, as an integer, for records
  ##
  minTTL: 10
  ## @param ns1.apiKey When using the ns1 provider, specify the API key to use
  ##
  apiKey: ""
  ## @param ns1.secretName Use an existing secret with key "ns1-api-key" defined.
  ## This ignores ns1.apiToken
  ##
  secretName: ""
## Pi-hole configuration to be set via arguments/env. variables
##
pihole:
  ## @param pihole.server When using the Pi-hole provider, specify The address of the Pi-hole web server
  ##
  server: ""
  ## @param pihole.tlsSkipVerify When using the Pi-hole provider, specify wheter to skip verification of any TLS certificates served by the Pi-hole web server
  ##
  tlsSkipVerify: ""
  ## @param pihole.password When using the Pi-hole provider, specify a password to use
  ##
  password: ""
  ## @param pihole.secretName Use an existing secret with key "pihole_password" defined.
  ##
  secretName: ""
## Compatibility adaptations for Traefik
##
traefik:
  ## @param traefik.disableNew Disable listeners on Resources under traefik.io
  ##
  disableNew: false
  ## @param traefik.disableLegacy Disable listeners on Resources under traefik.containo.us
  ##
  disableLegacy: false
## oci configuration to be set via arguments/env. variables
##
oci:
  ## @param oci.region When using the OCI provider, specify the region, where your zone is located in.
  ##
  region: ""
  ## @param oci.tenancyOCID When using the OCI provider, specify your Tenancy OCID
  ##
  tenancyOCID: ""
  ## @param oci.userOCID When using the OCI provider, specify your User OCID
  ##
  userOCID: ""
  ## @param oci.compartmentOCID When using the OCI provider, specify your Compartment OCID where your DNS Zone is located in.
  ##
  compartmentOCID: ""
  ## @param oci.privateKey [string] When using the OCI provider, paste in your RSA private key file for the Oracle API
  ##
  privateKey: |
    -----BEGIN RSA PRIVATE KEY-----
    -----END RSA PRIVATE KEY-----
  ## @param oci.privateKeyFingerprint When using the OCI provider, put in the fingerprint of your privateKey
  ##
  privateKeyFingerprint: ""
  ## @param oci.privateKeyPassphrase When using the OCI provider and your privateKey has a passphrase, put it in here. (optional)
  ##
  privateKeyPassphrase: ""
  ## @param oci.secretName When using the OCI provider, it's the name of the secret containing `oci.yaml` file.
  ## Ref: https://github.com/kubernetes-sigs/external-dns/blob/master/docs/tutorials/oracle.md#deploy-externaldns
  ##
  secretName: ""
  ## @param oci.useInstancePrincipal When using the OCI provider, enable IAM Instance Principal
  ## Ref: https://github.com/kubernetes-sigs/external-dns/blob/master/docs/tutorials/oracle.md#oci-iam-instance-principal
  useInstancePrincipal: false
  ## @param oci.useWorkloadIdentity When using the OCI provider, enable IAM Workload Identity
  ## Ref: https://github.com/kubernetes-sigs/external-dns/blob/master/docs/tutorials/oracle.md#oci-iam-instance-principal
  useWorkloadIdentity: false
## OVH configuration to be set via arguments/env. variables
##
ovh:
  ## @param ovh.consumerKey When using the OVH provider, specify the existing consumer key. (required when provider=ovh and `ovh.secretName` is not provided.)
  ##
  consumerKey: ""
  ## @param ovh.applicationKey When using the OVH provider with an existing application, specify the application key. (required when provider=ovh and `ovh.secretName` is not provided.)
  ##
  applicationKey: ""
  ## @param ovh.applicationSecret When using the OVH provider with an existing application, specify the application secret. (required when provider=ovh and `ovh.secretName` is not provided.)
  ##
  applicationSecret: ""
  ## @param ovh.secretName When using the OVH provider, it's the name of the secret containing `ovh_consumer_key`, `ovh_application_key` and `ovh_application_secret`. Disables usage of other `ovh`.
  ## with following keys:
  ##  - ovh_consumer_key
  ##  - ovh_application_key
  ##  - ovh_application_secret
  ## This ignores consumerKey, applicationKey & applicationSecret
  ##
  secretName: ""
## Scaleway configuration to be set via arguments/env. variables
##
scaleway:
  ## @param scaleway.scwAccessKey When using the Scaleway provider, specify an existing access key. (required when provider=scaleway)
  ##
  scwAccessKey: ""
  ## @param scaleway.scwSecretKey When using the Scaleway provider, specify an existing secret key. (required when provider=scaleway)
  ##
  scwSecretKey: ""
  ## @param scaleway.secretName Use an existing secret with keys "scaleway_access_key" and "scaleway_secret_key" defined (optional).
  ## This ignores scaleway.scwAccessKey and scaleway.scwSecretKey
  ##
  secretName: ""
## RFC 2136 configuration to be set via arguments/env. variables
##
rfc2136:
  ## @param rfc2136.host When using the rfc2136 provider, specify the RFC2136 host (required when provider=rfc2136)
  ##
  host: ""
  ## @param rfc2136.port When using the rfc2136 provider, specify the RFC2136 port (optional)
  ##
  port: 53
  ## @param rfc2136.zone DEPRECATED: use rfc2136.zones instead.
  ## Currently, the contents of rfc2136.zone and rfc2136.zones will be merged and deduplicated, in a future release this value will be removed in favor of `rfc2136.zones`.
  ##
  zone: ""
  ## @param rfc2136.zones When using the rfc2136 provider, specify the zones (required when provider=rfc2136 and `rfc2136.zone` is not provided.)
  ##
  zones: []
  ## @param rfc2136.tsigSecret When using the rfc2136 provider, specify the tsig secret to enable security. (do not specify if `rfc2136.secretName` is provided.) (optional)
  ##
  tsigSecret: ""
  ## @param rfc2136.secretName When using the rfc2136 provider, specify the existing secret which contains your tsig secret in the key "rfc2136_tsig_secret". Disables the usage of `rfc2136.tsigSecret` (optional)
  ##
  secretName: ""
  ## @param rfc2136.tsigSecretAlg When using the rfc2136 provider, specify the tsig secret to enable security (optional)
  ##
  tsigSecretAlg: hmac-sha256
  ## @param rfc2136.tsigKeyname When using the rfc2136 provider, specify the tsig keyname to enable security (optional)
  ##
  tsigKeyname: rfc2136_tsig_secret
  ## @param rfc2136.tsigAxfr When using the rfc2136 provider, enable AFXR to enable security (optional)
  ##
  tsigAxfr: true
  ## @param rfc2136.minTTL When using the rfc2136 provider, specify minimal TTL (in duration format) for records[ns, us, ms, s, m, h], see more <https://golang.org/pkg/time/#ParseDuration>
  ##
  minTTL: "0s"
  ## @param rfc2136.rfc3645Enabled When using the rfc2136 provider, extend using RFC3645 to support secure updates over Kerberos with GSS-TSIG
  ##
  rfc3645Enabled: false
  ## @param rfc2136.kerberosConfig When using the rfc2136 provider with rfc3645Enabled, the contents of a configuration file for krb5 (optional)
  ##
  kerberosConfig: ""
  ## @param rfc2136.kerberosUsername When using the rfc2136 provider with rfc3645Enabled, specify the username to authenticate with (optional)
  ##
  kerberosUsername: ""
  ## @param rfc2136.kerberosPassword When using the rfc2136 provider with rfc3645Enabled, specify the password to authenticate with (optional)
  ##
  kerberosPassword: ""
  ## @param rfc2136.kerberosRealm When using the rfc2136 provider with rfc3645Enabled, specify the realm to authenticate to (required when provider=rfc2136 and rfc2136.rfc3645Enabled=true)
  ##
  kerberosRealm: ""
## PowerDNS configuration to be set via arguments/env. variables
##
pdns:
  ## @param pdns.apiUrl When using the PowerDNS provider, specify the API URL of the server.
  ##
  apiUrl: ""
  ## @param pdns.apiPort When using the PowerDNS provider, specify the API port of the server.
  ##
  apiPort: "8081"
  ## @param pdns.apiKey When using the PowerDNS provider, specify the API key of the server.
  ##
  apiKey: ""
  ## @param pdns.secretName When using the PowerDNS provider, specify as secret name containing the API Key
  ##
  secretName: ""
## TransIP configuration to be set via arguments/env. variables
##
transip:
  ## @param transip.account When using the TransIP provider, specify the account name.
  ##
  account: ""
  ## @param transip.apiKey When using the TransIP provider, specify the API key to use.
  ##
  apiKey: ""
## VinylDNS configuration to be set via arguments/env. variables
##
vinyldns:
  ## @param vinyldns.host When using the VinylDNS provider, specify the VinylDNS API host.
  ##
  host: ""
  ## @param vinyldns.accessKey When using the VinylDNS provider, specify the Access Key to use.
  ##
  accessKey: ""
  ## @param vinyldns.secretKey When using the VinylDNS provider, specify the Secret key to use.
  ##
  secretKey: ""
## @param domainFilters Limit possible target zones by domain suffixes (optional)
##
domainFilters: []
## @param excludeDomains Exclude subdomains (optional)
##
excludeDomains: []
## @param regexDomainFilter Limit possible target zones by regex domain suffixes (optional)
## If regexDomainFilterl0 is specified, domainFilters will be ignored
##
regexDomainFilter: ""
## @param regexDomainExclusion Exclude subdomains by using regex pattern (optional)
## If regexDomainFilter is specified, excludeDomains will be ignored and external-dns will use regexDomainExclusion even though regexDomainExclusion is empty
##
regexDomainExclusion: ""
## @param zoneNameFilters Filter target zones by zone domain (optional)
##
zoneNameFilters: []
## @param zoneIdFilters Limit possible target zones by zone id (optional)
##
zoneIdFilters: []
## @param annotationFilter Filter sources managed by external-dns via annotation using label selector (optional)
##
annotationFilter: ""
## @param labelFilter Select sources managed by external-dns using label selector (optional)
##
labelFilter: ""
## @param ingressClassFilters Filter sources managed by external-dns via IngressClass (optional)
##
ingressClassFilters: []
## @param managedRecordTypesFilters Filter record types managed by external-dns (optional)
##
managedRecordTypesFilters: []
## @param dryRun When enabled, prints DNS record changes rather than actually performing them (optional)
##
dryRun: false
## @param triggerLoopOnEvent When enabled, triggers run loop on create/update/delete events in addition to regular interval (optional)
##
triggerLoopOnEvent: false
## @param interval Interval update period to use
##
interval: "1m"
## @param logLevel Verbosity of the logs (options: panic, debug, info, warning, error, fatal, trace)
##
logLevel: info
## @param logFormat Which format to output logs in (options: text, json)
##
logFormat: text
## @param policy Modify how DNS records are synchronized between sources and providers (options: sync, upsert-only )
##
policy: upsert-only
## @param registry Registry method to use (options: txt, aws-sd, dynamodb, noop)
## ref: https://github.com/kubernetes-sigs/external-dns/blob/master/docs/registry/registry.md
##
registry: "txt"
## @param txtPrefix When using the TXT registry, a prefix for ownership records that avoids collision with CNAME entries (optional)<CNAME record> (Mutual exclusive with txt-suffix)
##
txtPrefix: ""
## @param txtSuffix When using the TXT registry, a suffix for ownership records that avoids collision with CNAME entries (optional)<CNAME record>.suffix (Mutual exclusive with txt-prefix)
##
txtSuffix: ""
## @param txtOwnerId A name that identifies this instance of ExternalDNS. Currently used by registry types: txt & aws-sd (optional)
## But other registry types might be added in the future.
##
txtOwnerId: ""
## @param forceTxtOwnerId (backward compatibility) When using the non-TXT registry, it will pass the value defined by `txtOwnerId` down to the application (optional)
## This setting added for backward compatibility for
## customers who already used bitnami/external-dns helm chart
## to privision 'aws-sd' registry type.
## Previously bitnami/external-dns helm chart did not pass
## txtOwnerId value down to the external-dns application
## so the app itself sets that value to be a string 'default'.
## If existing customers force the actual txtOwnerId value to be
## passed properly, their external-dns updates will stop working
## because the owner's value for exting DNS records in
## AWS Service Discovery would remain 'default'.
## NOTE: It is up to the end user to update AWS Service Discovery
## 'default' values in description fields to make it work with new
## value passed as txtOwnerId when forceTxtOwnerId=true
##
forceTxtOwnerId: false
## TXT registry record encryption
## ref: https://github.com/kubernetes-sigs/external-dns/blob/master/docs/registry/txt.md#encryption
##
txtEncrypt:
  ## @param txtEncrypt.enabled Enable TXT record encrypencryption
  ##
  enabled: false
  ## @param txtEncrypt.aesKey 32-byte AES-256-GCM encryption key.
  ## ref: https://github.com/kubernetes-sigs/external-dns/blob/master/docs/registry/txt.md#generating-the-txt-encryption-key
  ## Note: If txtEncrypt.enabled is enabled and txtEncrypt.aesKey is not set a default key will be generated.
  ##
  aesKey: ""
  ## @param txtEncrypt.secretName Use an existing secret with key "txt_aes_encryption_key" defined.
  ## This ignores txtEncrypt.aesKey
  ##
  secretName: ""
## @param extraArgs Extra arguments to be passed to external-dns
##
extraArgs: {}
##extraArgs:
##  rfc2136-zone:
##    - extra-zone1.com
##    - extra-zone2.com
## @param extraEnvVars An array to add extra env vars
##
extraEnvVars: []
## @param extraEnvVarsCM ConfigMap containing extra env vars
##
extraEnvVarsCM: ""
## @param extraEnvVarsSecret Secret containing extra env vars (in case of sensitive data)
##
extraEnvVarsSecret: ""
## @param lifecycleHooks [object] Override default etcd container hooks
##
lifecycleHooks: {}
## @param schedulerName Alternative scheduler
## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
##
schedulerName: ""
## @param topologySpreadConstraints Topology Spread Constraints for pod assignment
## https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/
## The value is evaluated as a template
##
topologySpreadConstraints: []
## @param podAffinityPreset Pod affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
##
podAffinityPreset: ""
## @param podAntiAffinityPreset Pod anti-affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
## Allowed values: soft, hard
##
podAntiAffinityPreset: soft
## Node affinity preset
## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
##
nodeAffinityPreset:
  ## @param nodeAffinityPreset.type Node affinity preset type. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ##
  type: ""
  ## @param nodeAffinityPreset.key Node label key to match Ignored if `affinity` is set.
  ## E.g.
  ## key: "kubernetes.io/e2e-az-name"
  ##
  key: ""
  ## @param nodeAffinityPreset.values Node label values to match. Ignored if `affinity` is set.
  ## E.g.
  ## values:
  ##   - e2e-az1
  ##   - e2e-az2
  ##
  values: []
## @param affinity Affinity for pod assignment
## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
## Note: podAffinityPreset, podAntiAffinityPreset, and  nodeAffinityPreset will be ignored when it's set
##
affinity: {}
## @param nodeSelector Node labels for pod assignment
## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
##
nodeSelector: {}
## @param tolerations Tolerations for pod assignment
## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
##
tolerations: []
## @param podAnnotations Additional annotations to apply to the pod.
##
podAnnotations: {}
## @param podLabels Additional labels to be added to pods
##
podLabels: {}
## @param priorityClassName priorityClassName
##
priorityClassName: ""
## @param secretAnnotations Additional annotations to apply to the secret
##
secretAnnotations: {}
## Options for the source type "crd"
##
crd:
  ## @param crd.create Install and use the integrated DNSEndpoint CRD
  ##
  create: false
  ## @param crd.apiversion Sets the API version for the CRD to watch
  ##
  apiversion: ""
  ## @param crd.kind Sets the kind for the CRD to watch
  ##
  kind: ""
## Kubernetes svc configutarion
##
service:
  ## @param service.enabled Whether to create Service resource or not
  ##
  enabled: true
  ## @param service.type Kubernetes Service type
  ##
  type: ClusterIP
  ## @param service.ports.http ExternalDNS client port
  ##
  ports:
    http: 7979
  ## @param service.nodePorts.http Port to bind to for NodePort service type (client port)
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
  ##
  nodePorts:
    http: ""
  ## @param service.clusterIP IP address to assign to service
  ##
  clusterIP: ""
  ## @param service.externalIPs Service external IP addresses
  ##
  externalIPs: []
  ## @param service.externalName Service external name
  ##
  externalName: ""
  ## @param service.loadBalancerIP IP address to assign to load balancer (if supported)
  ##
  loadBalancerIP: ""
  ## @param service.loadBalancerSourceRanges List of IP CIDRs allowed access to load balancer (if supported)
  ##
  loadBalancerSourceRanges: []
  ## @param service.externalTrafficPolicy Enable client source IP preservation
  ## ref http://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
  ##
  externalTrafficPolicy: Cluster
  ## @param service.extraPorts Extra ports to expose in the service (normally used with the `sidecar` value)
  ##
  extraPorts: []
  ## @param service.annotations Annotations to add to service
  ## set the LoadBalancer service type to internal only.
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
  ##
  annotations: {}
  ## @param service.labels Provide any additional labels which may be required.
  ## This can be used to have external-dns show up in `kubectl cluster-info`
  ##  kubernetes.io/cluster-service: "true"
  ##  kubernetes.io/name: "external-dns"
  ##
  labels: {}
  ## @param service.sessionAffinity Session Affinity for Kubernetes service, can be "None" or "ClientIP"
  ## If "ClientIP", consecutive client requests will be directed to the same Pod
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies
  ##
  sessionAffinity: None
  ## @param service.sessionAffinityConfig Additional settings for the sessionAffinity
  ## sessionAffinityConfig:
  ##   clientIP:
  ##     timeoutSeconds: 300
  ##
  sessionAffinityConfig: {}
## Network Policies
## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
##
networkPolicy:
  ## @param networkPolicy.enabled Specifies whether a NetworkPolicy should be created
  ##
  enabled: true
  ## @param networkPolicy.allowExternal Don't require server label for connections
  ## The Policy model to apply. When set to false, only pods with the correct
  ## server label will have network access to the ports server is listening
  ## on. When true, server will accept connections from any source
  ## (with the correct destination port).
  ##
  allowExternal: true
  ## @param networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
  ##
  allowExternalEgress: true
  ## @param networkPolicy.kubeAPIServerPorts [array] List of possible endpoints to kube-apiserver (limit to your cluster settings to increase security)
  ##
  kubeAPIServerPorts: [443, 6443, 8443]
  ## @param networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolicy
  ## e.g:
  ## extraIngress:
  ##   - ports:
  ##       - port: 1234
  ##     from:
  ##       - podSelector:
  ##           - matchLabels:
  ##               - role: frontend
  ##       - podSelector:
  ##           - matchExpressions:
  ##               - key: role
  ##                 operator: In
  ##                 values:
  ##                   - frontend
  extraIngress: []
  ## @param networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
  ## e.g:
  ## extraEgress:
  ##   - ports:
  ##       - port: 1234
  ##     to:
  ##       - podSelector:
  ##           - matchLabels:
  ##               - role: frontend
  ##       - podSelector:
  ##           - matchExpressions:
  ##               - key: role
  ##                 operator: In
  ##                 values:
  ##                   - frontend
  ##
  extraEgress: []
  ## @param networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
  ## @param networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
  ##
  ingressNSMatchLabels: {}
  ingressNSPodMatchLabels: {}
## ServiceAccount parameters
## https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
##
serviceAccount:
  ## @param serviceAccount.create Determine whether a Service Account should be created or it should reuse a exiting one.
  ##
  create: true
  ## @param serviceAccount.name ServiceAccount to use. A name is generated using the common.names.fullname template if it is not set
  ##
  name: ""
  ## @param serviceAccount.annotations Additional Service Account annotations
  ##
  annotations: {}
  ## @param serviceAccount.automountServiceAccountToken Automount API credentials for a service account.
  ##
  automountServiceAccountToken: false
  ## @param serviceAccount.labels [object] Additional labels to be included on the service account
  ##
  labels: {}
## RBAC parameters
## https://kubernetes.io/docs/reference/access-authn-authz/rbac/
##
rbac:
  ## @param rbac.create Whether to create & use RBAC resources or not
  ##
  create: true
  ## @param rbac.clusterRole Whether to create Cluster Role. When set to false creates a Role in `namespace`
  ##
  clusterRole: true
  ## @param rbac.apiVersion Version of the RBAC API
  ##
  apiVersion: v1
  ## @param rbac.pspEnabled Whether to create a PodSecurityPolicy. WARNING: PodSecurityPolicy is deprecated in Kubernetes v1.21 or later, unavailable in v1.25 or later
  ##
  pspEnabled: false
## @param containerSecurityContext.enabled Enabled Apache Server containers' Security Context
## @param containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
## @param containerSecurityContext.runAsUser Set ExternalDNS containers' Security Context runAsUser
## @param containerSecurityContext.runAsGroup Set ExternalDNS containers' Security Context runAsGroup
## @param containerSecurityContext.runAsNonRoot Set ExternalDNS container's Security Context runAsNonRoot
## @param containerSecurityContext.privileged Set primary container's Security Context privileged
## @param containerSecurityContext.allowPrivilegeEscalation Set primary container's Security Context allowPrivilegeEscalation
## @param containerSecurityContext.capabilities.drop List of capabilities to be dropped
## @param containerSecurityContext.readOnlyRootFilesystem Set container readonlyRootFilesystem
## @param containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
## https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
## Example:
## containerSecurityContext:
##   allowPrivilegeEscalation: false
##   readOnlyRootFilesystem: true
##   capabilities:
##     drop: ["ALL"]
##
containerSecurityContext:
  enabled: true
  seLinuxOptions: {}
  runAsUser: 1001
  runAsGroup: 1001
  runAsNonRoot: true
  privileged: false
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop: ["ALL"]
  seccompProfile:
    type: "RuntimeDefault"
## @param podSecurityContext.enabled Enable pod security context
## @param podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
## @param podSecurityContext.sysctls Set kernel settings using the sysctl interface
## @param podSecurityContext.supplementalGroups Set filesystem extra groups
## @param podSecurityContext.fsGroup Group ID for the container
##
podSecurityContext:
  enabled: true
  fsGroupChangePolicy: Always
  sysctls: []
  supplementalGroups: []
  fsGroup: 1001
## Container resource requests and limits
## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
## We usually recommend not to specify default resources and to leave this as a conscious
## choice for the user. This also increases chances charts run on environments with little
## resources, such as Minikube. If you do want to specify resources, uncomment the following
## lines, adjust them as necessary, and remove the curly braces after 'resources:'.
## @param resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if resources is set (resources is recommended for production).
## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
##
resourcesPreset: "nano"
## @param resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
## Example:
## resources:
##   requests:
##     cpu: 2
##     memory: 512Mi
##   limits:
##     cpu: 3
##     memory: 1024Mi
##
resources: {}
## Configure extra options for liveness probe
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
## @param livenessProbe.enabled Enable livenessProbe
## @param livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
## @param livenessProbe.periodSeconds Period seconds for livenessProbe
## @param livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
## @param livenessProbe.failureThreshold Failure threshold for livenessProbe
## @param livenessProbe.successThreshold Success threshold for livenessProbe
##
livenessProbe:
  enabled: true
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 2
  successThreshold: 1
## Configure extra options for readiness probe
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
## @param readinessProbe.enabled Enable readinessProbe
## @param readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
## @param readinessProbe.periodSeconds Period seconds for readinessProbe
## @param readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
## @param readinessProbe.failureThreshold Failure threshold for readinessProbe
## @param readinessProbe.successThreshold Success threshold for readinessProbe
##
readinessProbe:
  enabled: true
  initialDelaySeconds: 5
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
## Configure extra options for startup probe
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-startup-probes/#configure-probes
## @param startupProbe.enabled Enable startupProbe
## @param startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
## @param startupProbe.periodSeconds Period seconds for startupProbe
## @param startupProbe.timeoutSeconds Timeout seconds for startupProbe
## @param startupProbe.failureThreshold Failure threshold for startupProbe
## @param startupProbe.successThreshold Success threshold for startupProbe
##
startupProbe:
  enabled: false
  initialDelaySeconds: 5
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
## @param customLivenessProbe Override default liveness probe
##
customLivenessProbe: {}
## @param customReadinessProbe Override default readiness probe
##
customReadinessProbe: {}
## @param customStartupProbe Override default startup probe
##
customStartupProbe: {}
## @param extraVolumes A list of volumes to be added to the pod
##
extraVolumes: []
## @param extraVolumeMounts A list of volume mounts to be added to the pod
##
extraVolumeMounts: []
## @param pdb.create Enable/disable a Pod Disruption Budget creation
## @param pdb.minAvailable Minimum number/percentage of pods that should remain scheduled
## @param pdb.maxUnavailable Maximum number/percentage of pods that may be made unavailable. Defaults to `1` if both `pdb.minAvailable` and `pdb.maxUnavailable` are empty.
##
pdb:
  create: true
  minAvailable: ""
  maxUnavailable: ""
## Prometheus Exporter / Metrics
##
metrics:
  ## @param metrics.enabled Enable prometheus to access external-dns metrics endpoint
  ##
  enabled: false
  ## @param metrics.podAnnotations Annotations for enabling prometheus to access the metrics endpoint
  ##
  podAnnotations: {}
  ## Prometheus Operator ServiceMonitor configuration
  ##
  serviceMonitor:
    ## @param metrics.serviceMonitor.enabled Create ServiceMonitor object
    ##
    enabled: false
    ## @param metrics.serviceMonitor.namespace Namespace in which Prometheus is running
    ##
    namespace: ""
    ## @param metrics.serviceMonitor.interval Interval at which metrics should be scraped
    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
    ##
    interval: ""
    ## @param metrics.serviceMonitor.scrapeTimeout Timeout after which the scrape is ended
    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
    ##
    scrapeTimeout: ""
    ## @param metrics.serviceMonitor.selector Additional labels for ServiceMonitor object
    ## ref: https://github.com/bitnami/charts/tree/main/bitnami/prometheus-operator#prometheus-configuration
    ## e.g:
    ## selector:
    ##   prometheus: my-prometheus
    ##
    selector: {}
    ## @param metrics.serviceMonitor.metricRelabelings Specify Metric Relabelings to add to the scrape endpoint
    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#relabelconfig
    ##
    metricRelabelings: []
    ## @param metrics.serviceMonitor.relabelings [array] Prometheus relabeling rules
    ##
    relabelings: []
    ## @param metrics.serviceMonitor.honorLabels Specify honorLabels parameter to add the scrape endpoint
    ##
    honorLabels: false
    ## @param metrics.serviceMonitor.labels Used to pass Labels that are required by the installed Prometheus Operator
    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#prometheusspec
    ##
    labels: {}
    ## @param metrics.serviceMonitor.targetLabels Labels from the Kubernetes service to be transferred to the created metrics
    targetLabels: []
    ## @param metrics.serviceMonitor.podTargetLabels Labels from the Kubernetes pod to be transferred to the created metrics
    podTargetLabels: []
    ## @param metrics.serviceMonitor.annotations Additional custom annotations for the ServiceMonitor
    ##
    annotations: {}
    ## @param metrics.serviceMonitor.jobLabel The name of the label on the target service to use as the job name in prometheus.
    ##
    jobLabel: ""
  ## Google Managed Prometheus PodMonitor configuration
  ##
  googlePodMonitor:
    ## @param metrics.googlePodMonitor.enabled Create Google Managed Prometheus PodMonitoring object
    ##
    enabled: false
    ## @param metrics.googlePodMonitor.namespace Namespace in which PodMonitoring created
    ##
    namespace: ""
    ## @param metrics.googlePodMonitor.interval Interval at which metrics should be scraped by Google Managed Prometheus
    ##
    interval: "60s"
    ## @param  metrics.googlePodMonitor.endpoint The endpoint for Google Managed Prometheus scraping the metrics
    ##
    endpoint: /metrics
