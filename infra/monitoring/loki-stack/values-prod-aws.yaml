loki:
  image:
    tag: 2.9.13
  isDefault: false
  enabled: true

  podAnnotations:
    prometheus.io/scrape: "false"
  config:
    compactor:
      compaction_interval: 1h
      retention_enabled: true
      retention_delete_delay: 1h
      # retention_delete_worker_count: 10
    query_scheduler:
      max_outstanding_requests_per_tenant: 4096
    frontend:
      max_outstanding_per_tenant: 4096
    query_range:
      parallelise_shardable_queries: true
    limits_config:
      volume_enabled: true
      split_queries_by_interval: 15m
      max_query_parallelism: 32
      retention_period: 14d
      retention_stream:
        - selector: '{namespace=~"(wyden-dev|wyden-qa|wyden-runner)"}'
          priority: 1
          period: 7d
    table_manager:
      retention_deletes_enabled: true
      retention_period: 14d
  persistence:
    enabled: true
    accessModes: ["ReadWriteOnce"]
    size: 160Gi

  resources:
    limits:
      cpu: 1
      memory: 4Gi
    requests:
      cpu: 1
      memory: 1Gi

  ingress:
    enabled: true
    ingressClassName: "nginx"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-wyden"
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/auth-secret: basic-auth
      nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    hosts:
      - host: logs-aws.wyden.io
        paths:
          - /
    tls:
      - hosts:
          - logs-aws.wyden.io
        secretName: loki-tls

promtail:
  enabled: true
  image:
    registry: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 20m
      memory: 64Mi
  nodeSelector:
    kubernetes.io/os: linux
  tolerations:
    - effect: NoSchedule
      operator: Exists
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: eks.amazonaws.com/compute-type
                operator: NotIn
                values:
                  - fargate
