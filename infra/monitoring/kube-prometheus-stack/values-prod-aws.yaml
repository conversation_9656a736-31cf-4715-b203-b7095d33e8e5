prometheus:
  thanosService:
    enabled: true
  prometheusSpec:
    retention: 14d
    retentionSize: 38GB
    externalLabels:
      cluster: "wyden-aws-cloud"
      environment: "development"
    ## Remote write is required for Metrics from traces in Tempo
    ## https://grafana.com/docs/tempo/latest/getting-started/metrics-from-traces/
    enableRemoteWriteReceiver: true
    probeSelectorNilUsesHelmValues: false
    podMonitorSelectorNilUsesHelmValues: false
    serviceMonitorSelectorNilUsesHelmValues: false
    storageSpec:
      volumeClaimTemplate:
        spec:
          # storageClassName: azurefile-csi-nfs
          #storageClassName: gp3
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 40Gi
    resources:
      limits:
        cpu: "1"
        memory: 4Gi
      requests:
        cpu: "1"
        memory: 3Gi
    thanos:
      version: 0.35.1

    additionalScrapeConfigs:
      - job_name: kubernetes-service-endpoints
        kubernetes_sd_configs:
          - role: service
        relabel_configs:
          # annotation 'prometheus.io/scrape' must be set to 'true'
          - action: keep
            regex: true
            source_labels:
              [__meta_kubernetes_service_annotation_prometheus_io_scrape]

          # allow override of http scheme
          - action: replace
            regex: (https?)
            source_labels:
              [__meta_kubernetes_service_annotation_prometheus_io_scheme]
            target_label: __scheme__

          # allow override of default /metrics path
          - action: replace
            regex: (.+)
            source_labels:
              [__meta_kubernetes_service_annotation_prometheus_io_path]
            target_label: __metrics_path__

          # allow override of default port
          - action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            source_labels:
              [
                __address__,
                __meta_kubernetes_service_annotation_prometheus_io_port,
              ]
            target_label: __address__
          - { action: labelmap, regex: __meta_kubernetes_service_label_(.+) }
          - action: replace
            source_labels: [__meta_kubernetes_namespace]
            target_label: namespace
          - action: replace
            source_labels: [__meta_kubernetes_service_name]
            target_label: service

      - job_name: kubernetes-pods-endpoints
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          # annotation 'prometheus.io/scrape' must be set to 'true'
          - action: keep
            regex: true
            source_labels:
              [__meta_kubernetes_pod_annotation_prometheus_io_scrape]

          # allow override of http scheme
          - action: replace
            regex: (https?)
            source_labels:
              [__meta_kubernetes_pod_annotation_prometheus_io_scheme]
            target_label: __scheme__

          # allow override of default /metrics path
          - action: replace
            regex: (.+)
            source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            target_label: __metrics_path__

          # allow override of default port
          - action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            source_labels:
              [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: pod

  thanosIngress:
    enabled: true
    ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
      cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    hosts:
      - thanos-wyden-aws.wyden.io
    pathType: Prefix

    tls:
      - secretName: thanos-tls
        hosts:
          - thanos-wyden-aws.wyden.io

alertmanager:
  enabled: false

grafana:
  enabled: false

kubeControllerManager:
  enabled: false

kubeScheduler:
  enabled: false

kubeProxy:
  enabled: false

kubeEtcd:
  enabled: false

kubeApiServer:
  enabled: false

prometheus-node-exporter:
  resources:
    requests:
      cpu: 10m
      memory: 32Mi
    limits:
      cpu: 100m
      memory: 64Mi
