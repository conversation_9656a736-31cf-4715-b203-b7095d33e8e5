gitlabUrl: https://gitlab.wyden.io
runnerToken: glrt-********************
unregisterRunners: false

runners:
  tags: "kubernetes"
  runUntagged: true
  config: |
    [[runners]]
      [runners.kubernetes]
        namespace = "{{.Release.Namespace}}"
        service_account = "{{ include "gitlab-runner.fullname" . }}"
        image = "docker:20.10"
        privileged = true

        # build container
        cpu_limit = "4"
        cpu_request = "1"
        memory_limit = "4Gi"
        memory_request = "512Mi"

        # service containers
        service_cpu_request = "0.2"
        service_memory_request = "256Mi"
        service_cpu_limit = "1"
        service_memory_limit = "4Gi"

        # helper container
        helper_cpu_request = "0.3"
        helper_memory_request = "512Mi"
        helper_cpu_limit = "0.6"
        helper_memory_limit = "4Gi"

        poll_timeout = 3600
        image_pull_secrets = ["regcred"]
        helper_image = "gitlab/gitlab-runner-helper:alpine-latest-arm-latest"
        [runners.kubernetes.node_selector]
          environment = "service"
        [runners.kubernetes.node_tolerations]
          "service=true" = "NoSchedule"
          "kubernetes.azure.com/scalesetpriority=spot" = "NoSchedule"

      [runners.cache]
        Type = "azure"
        Path = "runner"
        Shared = true
        [runners.cache.azure]
          AccountName = "algotraderfiles"
          AccountKey = "****************************************************************************************"
          ContainerName = "runners-cache"
          StorageDomain = "blob.core.windows.net"

rbac:
  create: true
  clusterWideAccess: true
  rules:
    - apiGroups:
        [
          "",
          "apps",
          "rbac.authorization.k8s.io",
          "networking.k8s.io",
          "monitoring.coreos.com",
          "policy",
        ]
      resources: ["*"]
      verbs: ["*"]

serviceAccount:
  create: true

nodeSelector:
  environment: service
tolerations:
  - key: "service"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
  - key: "kubernetes.azure.com/scalesetpriority"
    operator: "Equal"
    value: "spot"
    effect: "NoSchedule"
