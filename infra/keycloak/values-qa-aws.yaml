global:
  imageRegistry: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub

auth:
  adminUser: admin
  adminPassword: 6FEmr1LDhnx9ff2

extraEnvVars:
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"

replicaCount: 2

ingress:
  enabled: true
  ingressClassName: nginx
  tls: true
  hostname: "keycloak-qa.wyden.io"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"

postgresql:
  enabled: false

externalDatabase:
  host: wyden-develop.cluster-cx2cemkicqia.eu-central-1.rds.amazonaws.com
  user: wyden
  password: iHuB77uDge5ERgbs
  database: keycloak_qa

nodeSelector:
  environment: qa
tolerations:
  - key: "qa"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

resources:
  requests:
    cpu: 250m
    memory: 512Mi
  limits:
    cpu: 1000m
    memory: 2Gi

affinity: |
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: "{{ .Release.Name }}"
        topologyKey: kubernetes.io/hostname
