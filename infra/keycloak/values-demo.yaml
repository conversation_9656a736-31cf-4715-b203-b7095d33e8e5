auth:
  adminUser: admin
  adminPassword: "!2YR^LssS#YUEP%EhD1"

extraEnvVars:
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"

replicaCount: 2

cache:
  enabled: false

ingress:
  enabled: true
  ingressClassName: nginx
  tls: true
  hostname: "keycloak-demo.wyden.io"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"

externalDatabase:
  host: wyden-cloud-demo.postgres.database.azure.com
  user: wyden
  password: d2twKSmX5Wjv
  database: keycloak_demo

nodeSelector:
  environment: demo
tolerations:
  - key: "demo"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
  - key: "kubernetes.azure.com/scalesetpriority"
    operator: "Equal"
    value: "spot"
    effect: "NoSchedule"

resources:
  requests:
    cpu: 250m
    memory: 512Mi
  limits:
    cpu: 1000m
    memory: 2Gi
