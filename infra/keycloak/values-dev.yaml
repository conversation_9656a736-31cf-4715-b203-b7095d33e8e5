auth:
  adminUser: admin
  adminPassword: 'Z#c9YgatDgr\,M}t'

extraEnvVars:
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"

replicaCount: 2

ingress:
  enabled: true
  ingressClassName: nginx
  tls: true
  hostname: "keycloak-dev.wyden.io"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"

postgresql:
  enabled: false

externalDatabase:
  host: mvp-dev.postgres.database.azure.com
  user: wyden
  password: iHuB77uDge5ERgbs
  database: keycloak_dev

nodeSelector:
  environment: dev
tolerations:
  - key: "dev"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
  - key: "kubernetes.azure.com/scalesetpriority"
    operator: "Equal"
    value: "spot"
    effect: "NoSchedule"

resources:
  requests:
    cpu: 250m
    memory: 512Mi
  limits:
    cpu: 1000m
    memory: 2Gi

affinity: |
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: "{{ .Release.Name }}"
        topologyKey: kubernetes.io/hostname
