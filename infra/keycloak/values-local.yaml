auth:
  adminUser: admin
  adminPassword: "makeitwyden"

proxy: reencrypt
httpRelativePath: "/"

externalDatabase:
  host: postgresql
  user: admin
  password: admin
  database: keycloak

resources:
  limits:
    cpu: 1
    memory: 512Mi
  requests:
    cpu: 0.25
    memory: 256Mi

livenessProbe:
  timeoutSeconds: 10

ingress:
  enabled: true
  tls: false
  hostname: "keycloak.wyden.io"
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
