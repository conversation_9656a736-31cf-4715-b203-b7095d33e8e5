# Default values for vault-backup.

extraLabels:
  azure.workload.identity/use: "true"
# annotations:
#   azure.workload.identity/inject-proxy-sidecar: "true"
serviceAccount:
  enabled: true
  extraLabels:
    azure.workload.identity/use: "true"
  # annotations:
  #   azure.workload.identity/client-id: "88370a06-6e91-4ee9-9046-bbf7913eb514"

schedule: "0 3 * * *"
secret_name: "vault-backup-secrets"
vault_address: "http://vault:8200"
resources:
  requests:
    cpu: 100m
    memory: 64Mi
  limits:
    cpu: 250m
    memory: 500Mi
nodeSelector: {}
tolerations: []
affinity: {}
