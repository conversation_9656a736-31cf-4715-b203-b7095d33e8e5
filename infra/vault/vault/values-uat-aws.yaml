server:
  enabled: true
  updateStrategyType: "RollingUpdate"
  extraSecretEnvironmentVars:
    - envName: AWS_SECRET_ACCESS_KEY
      secretName: vault-aws-unseal
      secretKey: AWS_SECRET_ACCESS_KEY
    - envName: AWS_ACCESS_KEY_ID
      secretName: vault-aws-unseal
      secretKey: AWS_ACCESS_KEY_ID

  ingress:
    enabled: true
    ingressClassName: "nginx"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    hosts:
      - host: vault-uat.wyden.io
        paths: []
    tls:
      - hosts:
          - vault-uat.wyden.io
        secretName: vault-ng-tls

  image:
    repository: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/hashicorp/vault
    tag: 1.14.8

  resources:
    requests:
      memory: 256Mi
      cpu: 500m
    limits:
      memory: 512Mi
      cpu: 500m

  dataStorage:
    enabled: false
    size: 10Gi

  standalone:
    enabled: false
  ha:
    enabled: true
    replicas: 3
    config: |
      ui = true

      listener "tcp" {
        tls_disable = 1
        address = "[::]:8200"
        cluster_address = "[::]:8201"
      }

      disable_mlock = true

      seal "awskms" {
        region = "eu-central-1"
        kms_key_id = "6420b34f-1e2e-4a49-bb4b-9974a901db3f"
      }

      storage "postgresql" {
        connection_url = "postgresql://wyden:<EMAIL>:5432/vault_uat"
        table="vault_kv_store"
        ha_enabled=true
        ha_table="vault_ha_locks"
      }

  nodeSelector:
    environment: uat
  tolerations:
    - key: "uat"
      operator: "Equal"
      value: "true"
      effect: "NoSchedule"
injector:
  enabled: false
