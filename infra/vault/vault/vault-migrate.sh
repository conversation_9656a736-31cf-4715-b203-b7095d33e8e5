#!/bin/sh
set -e

apk add jq

export SOURCE_VAULT_ADDR=${SOURCE_VAULT_ADDR:-"https://vault-qa.wyden.io"}
export SOURCE_VAULT_TOKEN=${SOURCE_VAULT_TOKEN:-"hvs.CAESIPNephIoxXWgy_ZEaq0dqQlEl7jQTHsxT3ewr0UIxJoSGh4KHGh2cy5RYWxXbGljcEg2YkpQVXRCRmV0aFdXeGc"}

export DEST_VAULT_ADDR=${DEST_VAULT_ADDR:-"http://vault-qa:8200"}
export DEST_VAULT_TOKEN=${DEST_VAULT_TOKEN:-"hvs.mbVqqLOq89OtSuwKmddOVlRn"}


SUBPATH="api-key connector-api-key"

# Create temporary directory for data
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: $TEMP_DIR"

# Function to check if vault is accessible
check_vault() {
    local vault_addr=$1
    local vault_token=$2

    VAULT_ADDR=$vault_addr VAULT_TOKEN=$vault_token vault status >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "Error: Unable to connect to Vault at $vault_addr"
        exit 1
    fi
}

# Check both vaults are accessible
echo "Checking source Vault connection..."
check_vault $SOURCE_VAULT_ADDR $SOURCE_VAULT_TOKEN

echo "Checking destination Vault connection..."
check_vault $DEST_VAULT_ADDR $DEST_VAULT_TOKEN

for subpath in $SUBPATH;
do
    mkdir "$TEMP_DIR/$subpath"
    # Export data from source vault
    echo "Exporting data from source Vault..."
    VAULT_ADDR=$SOURCE_VAULT_ADDR VAULT_TOKEN=$SOURCE_VAULT_TOKEN vault kv list -format=json "secret/$subpath/" | jq -r '.[]' | while IFS= read -r key; do
        echo "Exporting key: $key"
        VAULT_ADDR=$SOURCE_VAULT_ADDR VAULT_TOKEN=$SOURCE_VAULT_TOKEN vault kv get -format=json "secret/$subpath/$key" > "$TEMP_DIR/$subpath/$key.json"
    done
    # Import data to destination vault
    echo "Importing data to destination Vault..."
    for file in "$TEMP_DIR/$subpath"/*.json; do
        if [ -f "$file" ]; then
            key=$(basename "$file" .json | sed 's/\\ / /g')
            echo "Importing key: $key"
            cat "$file"
            cat "$file" | jq -r '.data.data' | VAULT_ADDR=$DEST_VAULT_ADDR VAULT_TOKEN=$DEST_VAULT_TOKEN vault kv put "secret/$subpath/$key" -
        fi
    done
done


# Cleanup
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo "Migration completed successfully!"