server:
  enabled: true
  extraSecretEnvironmentVars:
    - envName: AWS_SECRET_ACCESS_KEY
      secretName: vault-aws-unseal
      secretKey: AWS_SECRET_ACCESS_KEY
    - envName: AWS_ACCESS_KEY_ID
      secretName: vault-aws-unseal
      secretKey: AWS_ACCESS_KEY_ID

  standalone:
    enabled: false
  ha:
    enabled: true
    replicas: 3
    config: |
      ui = true

      listener "tcp" {
        tls_disable = 1
        address = "[::]:8200"
        cluster_address = "[::]:8201"
      }

      disable_mlock = true

      seal "awskms" {
        region = "eu-central-1"
        kms_key_id = "12936035-05ca-4acc-86cb-4497597da1d2"
      }

      service_registration "kubernetes" {}

      storage "raft" {
        path = "/vault/data"
      }
    raft:
      enabled: true
      setNodeId: true
      config: |
        ui = true
        disable_mlock = true

        listener "tcp" {
          tls_disable = 1
          address = "[::]:8200"
          cluster_address = "[::]:8201"
        }


        seal "awskms" {
          region = "eu-central-1"
          kms_key_id = "12936035-05ca-4acc-86cb-4497597da1d2"
        }

        service_registration "kubernetes" {}

        storage "raft" {
          path = "/vault/data"
          retry_join {
            leader_api_addr = "http://{{ .Release.Name }}-active:8200"
          }
        }

  image:
    repository: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/hashicorp/vault
    tag: 1.14.8

  ingress:
    enabled: true
    ingressClassName: "nginx"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    hosts:
      - host: vault-performance.wyden.io
        paths: []
    tls:
      - hosts:
          - vault-performance.wyden.io
        secretName: vault-ng-tls

  resources:
    requests:
      memory: 256Mi
      cpu: 500m
    limits:
      memory: 512Mi
      cpu: 500m

  dataStorage:
    enabled: true
    size: 10Gi

  nodeSelector:
    environment: performance
  tolerations:
    - key: "performance"
      operator: "Equal"
      value: "true"
      effect: "NoSchedule"

  annotations:
    karpenter.sh/do-not-disrupt: "true"

injector:
  enabled: false
