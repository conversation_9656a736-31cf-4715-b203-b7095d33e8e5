apiVersion: batch/v1
kind: Job
metadata:
  name: vault-job
spec:
  template:
    spec:
      containers:
        - name: vault
          image: hashicorp/vault:latest
          command: ["/bin/sh"]
          args: ["-c", "./vault-migrate.sh"]
          volumeMounts:
            - name: script-volume
              mountPath: /vault-migrate.sh
              subPath: vault-migrate.sh
      volumes:
        - name: script-volume
          configMap:
            name: vault-migrate-script
            defaultMode: 0755
      restartPolicy: OnFailure
