server:
  enabled: true
  updateStrategyType: "RollingUpdate"
  ### Need to be enabled for better security
  ### It uses node permissions at the moment
  ### Blocker is Azure because doesn't allow wildcard OIDC permission
  ### More details https://github.com/Azure/azure-workload-identity/issues/373
  # annotations:
  #   azure.workload.identity/inject-proxy-sidecar: "true"
  ### It requires Azure workload identity to be installed/enabled in the cluster
  extraLabels:
    azure.workload.identity/use: "true"
  serviceAccount:
    extraLabels:
      azure.workload.identity/use: "true"
    ### Need to be enabled for better security
    ### It uses node permissions at the moment
    ### Blocker is Azure because doesn't allow wildcard OIDC permission
    ### More details https://github.com/Azure/azure-workload-identity/issues/373
    # annotations:
    #   azure.workload.identity/client-id: "88370a06-6e91-4ee9-9046-bbf7913eb514"
  ingress:
    enabled: true
    ingressClassName: "nginx"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    hosts:
      - host: vault-ng.wyden.io
        paths: []
    tls:
      - hosts:
          - vault-ng.wyden.io
        secretName: vault-ng-tls

  extraSecretEnvironmentVars:
    - envName: AWS_SECRET_ACCESS_KEY
      secretName: vault-aws-unseal
      secretKey: AWS_SECRET_ACCESS_KEY
    - envName: AWS_ACCESS_KEY_ID
      secretName: vault-aws-unseal
      secretKey: AWS_ACCESS_KEY_ID
    - envName: AWS_REGION
      secretName: vault-aws-unseal
      secretKey: AWS_REGION
    - envName: VAULT_AWSKMS_SEAL_KEY_ID
      secretName: vault-aws-unseal
      secretKey: VAULT_AWSKMS_SEAL_KEY_ID

  image:
    tag: 1.14.8

  resources:
    requests:
      memory: 256Mi
      cpu: 500m
    limits:
      memory: 512Mi
      cpu: 500m

  standalone:
    enabled: false
  ha:
    enabled: true
    replicas: 3
    config: |
      ui = true

      listener "tcp" {
        tls_disable = 1
        address = "[::]:8200"
        cluster_address = "[::]:8201"
      }

      disable_mlock = true

      seal "awskms" {
      }

      service_registration "kubernetes" {}

      storage "raft" {
        path = "/vault/data"
      }

    raft:
      enabled: true
      setNodeId: true
      config: |
        ui = true
        disable_mlock = true

        listener "tcp" {
          tls_disable = 1
          address = "[::]:8200"
          cluster_address = "[::]:8201"
        }

        seal "awskms" {
        }

        service_registration "kubernetes" {}

        storage "raft" {
          path = "/vault/data"
          retry_join {
            leader_api_addr = "http://vault-active:8200"
          }
        }

    # extraVolumes:
    #   - type: configMap
    #     name: init-vault

    # postStart:
    #   - /bin/sh
    #   - -ce
    #   - cp /vault/userconfig/init-vault/init.sh /tmp/init.sh && chmod +x /tmp/init.sh && /tmp/init.sh > /tmp/init.log
    disruptionBudget:
      enabled: false

  dataStorage:
    enabled: true
    size: 10Gi

  nodeSelector:
    environment: dev
  tolerations:
    - key: "dev"
      operator: "Equal"
      value: "true"
      effect: "NoSchedule"
    - key: "kubernetes.azure.com/scalesetpriority"
      operator: "Equal"
      value: "spot"
      effect: "NoSchedule"

injector:
  enabled: false
