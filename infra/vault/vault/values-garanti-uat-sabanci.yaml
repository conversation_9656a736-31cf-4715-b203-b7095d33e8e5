server:
  enabled: true
  updateStrategyType: "RollingUpdate"
  extraLabels:
    azure.workload.identity/use: "true"
  serviceAccount:
    extraLabels:
      azure.workload.identity/use: "true"

  ingress:
    enabled: true
    ingressClassName: "nginx"
    hosts:
      - host: vault-garanti-sdx.wyden.io
        paths: []
    tls:
      - hosts:
          - vault-garanti-sdx.wyden.io
        secretName: vault-garanti-sdx

  image:
    tag: 1.14.8

  resources:
    requests:
      memory: 256Mi
      cpu: 100m
    limits:
      memory: 512Mi
      cpu: 300m

  ha:
    enabled: false
  standalone:
    enabled: "-"
    config: |
      ui = true

      listener "tcp" {
        tls_disable = 1
        address = "[::]:8200"
        cluster_address = "[::]:8201"
      }

      storage "postgresql" {
        connection_url = "**************************************************************"
        table = "vault_kv_store"
      }

  dataStorage:
    enabled: false
    size: 10Gi

  #nodeSelector:
  #  environment: garanti-uat
  #tolerations:
  #  - key: "garanti-uat"
  #    operator: "Equal"
  #    value: "true"
  #    effect: "NoSchedule"
injector:
  enabled: false
