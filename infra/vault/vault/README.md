# Vault
## Pre-requirements
### Add repo
```sh
helm repo add hashicorp https://helm.releases.hashicorp.com
```
### Create AWS credentials
If the AWS integration is planned to be enabled, the application has to have access to the AWS account
```sh
kubectl create secret generic vault-aws --namespace algotrader-dev --from-literal=AWS_REGION=us-east-1 --from-literal=AWS_ACCESS_KEY_ID= --from-literal=AWS_SECRET_ACCESS_KEY= --from-literal=VAULT_AWSKMS_SEAL_KEY_ID=
```
## Deploy
### Check difference before deploy
```sh
helm diff upgrade --install --namespace algotrader-demo vault-demo hashicorp/vault --version 0.25.0 -f infra/vault/vault/values-demo.yaml
```
### Deploy the service
```sh
helm upgrade --install --namespace algotrader-demo vault-demo hashicorp/vault --version 0.25.0 -f infra/vault/vault/values-demo.yaml
```
## Initialize the cluster
After the service is deployed it's necessary to initialize it. On the updates those steps are not needed.
On the first node:
```sh
kubectl exec -ti vault-0 -- vault operator init
```
Three times provide the key to every node:
```sh
kubectl exec -ti vault-0 -- vault operator unseal
```
More info availabe at: https://developer.hashicorp.com/vault/docs/platform/k8s/helm/run

### Enable secrets engine
Application is awaits for the secret engine to be available (it doesn't create it)
```sh
vault login -no-print <root_user_token>
vault secrets enable -path=secret -version=2 kv
```

## Migrate Cloud provider
If the auto-unseal for the cloud provider needs to be changed, disable the old (do not delete, just add `disabled = "true"` property in it's definition), restart POD, and execute next command:
https://developer.hashicorp.com/vault/docs/concepts/seal#migration-post-vault-1-5-1
```sh
vault operator unseal -migrate
```
Then apply final configuration that doesn't contain old cloud auto-unseal definition and restart POD


## Migrate storage provider
Install MySQL that will hold the configuration
`values.yaml`:
```yaml
auth:
  rootPassword: password
  username: admin
  password: somepassword
  database: vault

primary:
  extraFlags: "--log-bin-trust-function-creators=1"
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 500m
      memory: 512Mi
```
the command:
```sh
helm upgrade --install --namespace garanti-uat mysql bitnami/mysql --version 11.1.17 -f infra/mysql/values.yaml
```
Prepare a snapshot:
```sh
kubectl exec -ti -n garanti-uat vault-garanti-uat-0 -- sh

vault login
VAULT_ADDR=http://vault-garanti-uat:8200 vault operator raft snapshot save /tmp/vault.snap
```
Copy the snapshot locally
```sh
kubectl cp -n garanti-uat vault-garanti-uat-0:/tmp/vault.snap infra/vault/vault/vault.snap
```

Scale down the cluster and add sleep instead of the start command, to make sure Vault is not running:
```sh
kubectl scale sts -n garanti-uat --replicas 1 vault-garanti-uat
kubectl edit sts -n garanti-uat vault-garanti-uat
kubectl delete pod -n garanti-uat vault-garanti-uat-0
```
Add migration file on the Valut instance and perform the migration
migrate-p1-garanti.hcl:
```
storage_source "raft" {
  path = "/vault/data/"
  node_id = "vault-garanti-uat-0"
}

storage_destination "mysql" {
  address = "mysql:3306"
  username = "root"
  password = "password"
  database = "vault"
}

cluster_addr = "http://vault-garanti-uat-0.garanti-uat:8201"
```
commands:
```sh
kubectl exec -ti -n garanti-uat vault-garanti-uat-0 -- sh
vi /tmp/migrate-p1-garanti.hcl

vault operator migrate -config=/tmp/migrate-p1-garanti.hcl
```

Replace Vault RAFT with Vault Standalone (**do not initialize Vault!**)
```sh
helm uninstall -n garanti-uat vault-garanti-uat
kubectl delete pvc -n garanti-uat data-vault-garanti-uat-0

helm upgrade --install --namespace garanti-uat vault-garanti-uat hashicorp/vault --version 0.25.0 -f infra/vault/vault/values-garanti-uat.yaml
```

Migrate back all the settings and secrets from MySQL
migrate-p2-garanti.hcl:
```
storage_source "mysql" {
  address = "mysql:3306"
  username = "root"
  password = "password"
  database = "vault"
}

storage_destination "file" {
  path = "/vault/data/"
}

cluster_addr = "http://vault-garanti-uat-0.garanti-uat:8201"
```
commands:
```sh
kubectl exec -ti -n garanti-uat vault-garanti-uat-0 -- sh
vault operator migrate -config=/tmp/migrate-p2-garanti.hcl
```

Add init container
```sh
helm upgrade --install --namespace garanti-uat vault-garanti-uat hashicorp/vault --version 0.25.0 -f infra/vault/vault/values-garanti-uat.yaml -f infra/vault/vault/values-stand-init.yaml
```
Check if the old token still works and all secrets are in place:
```sh
kubectl port-forward -n garanti-uat svc/vault-garanti-uat 8200
```
Clean up
```sh
kubectl delete pvc -n garanti-uat data-vault-garanti-uat-1 data-vault-garanti-uat-2
helm uninstall -n garanti-uat mysql
kubectl delete pvc -n garanti-uat data-mysql-0
```

###

```sql
CREATE database vault;
\c vault
CREATE TABLE vault_kv_store (
  parent_path TEXT COLLATE "C" NOT NULL,
  path        TEXT COLLATE "C",
  key         TEXT COLLATE "C",
  value       BYTEA,
  CONSTRAINT pkey PRIMARY KEY (path, key)
);

CREATE INDEX parent_path_idx ON vault_kv_store (parent_path);
```
For HA Vault setup (not standalone):
```sql
CREATE TABLE vault_ha_locks (
  ha_key                                      TEXT COLLATE "C" NOT NULL,
  ha_identity                                 TEXT COLLATE "C" NOT NULL,
  ha_value                                    TEXT COLLATE "C",
  valid_until                                 TIMESTAMP WITH TIME ZONE NOT NULL,
  CONSTRAINT ha_key PRIMARY KEY (ha_key)
);
```

Add migration file on the Valut instance and perform the migration
migrate-vault-garanti-uat.hcl:
```json
storage_source "file" {
  path = "/vault/data"
  node_id = "vault-garanti-uat-0"
}

storage_destination "postgresql" {
  connection_url = "*********************************************/vault"
  table = "vault_kv_store"
  ha_enabled = "true"
  ha_table = "vault_ha_locks"
}

cluster_addr = "http://vault-garanti-uat-0.garanti-uat:8201"
```
commands:
```sh
kubectl cp -n garanti-uat infra/vault/vault/migrate-vault-garanti-uat.hcl vault-garanti-uat-0:/tmp/migrate-vault-garanti-uat.hcl
kubectl exec -ti -n garanti-uat vault-garanti-uat-0 -- sh

vault operator migrate -config=/tmp/migrate-vault-garanti-uat.hcl
```

## Migrate data only
Adjust details in the `vault-migrate.sh` and create configmap
```sh
kubectl create configmap vault-migrate-script --from-file=infra/vault/vault/vault-migrate.sh
```
Deploy Kubernetes job from `job-migrated.yaml`
```sh
kubectl apply -f infra/vault/vault/job-migrated.yaml
```