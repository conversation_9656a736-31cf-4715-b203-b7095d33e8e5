#!/bin/bash

# This script provides an easy way to open Gitlab merge request for given branch
# Usage: ./create_mr.sh BRANCH_NAME

starthere_path=$PWD
search_path=$starthere_path/..

branch_name=$1

if [ -z "$branch_name" ]; then
  echo "Error: Missing branch_name argument."
  echo "Usage: $0 branch_name"
  exit 255
fi

echo "Searching for git repos with branch $branch_name in $search_path"

MATCHING_PROJECT_PATHS=()

# Find all git repos
find "$search_path" -type d -name ".git" -print0 | while IFS= read -r -d '' dir; do
    # Find all local repositories containing $branch_name
    if git -C $dir show-ref --verify --quiet refs/heads/$branch_name; then
        (cd $dir && \
          # Get the URL of the origin remote repository
          REPO_URL=$(git config --get remote.origin.url)

          # Remove the protocol part (ssh://)
          PROJECT_PATH="${REPO_URL#*://}"

          # Remove everything until first slash
          PROJECT_PATH="${PROJECT_PATH#*/}"

          # Remove the .git from the end of the string
          PROJECT_PATH="${PROJECT_PATH%.git}"

          echo "Found branch $branch_name in $PROJECT_PATH"
          MATCHING_PROJECT_PATHS+=("$PROJECT_PATH")

          CREATE_MR_URL="https://gitlab.wyden.io/$PROJECT_PATH/-/merge_requests/new?merge_request%5Bsource_branch%5D=$branch_name"

          echo "Opening MR page for $PROJECT_PATH"
          open $CREATE_MR_URL
        )
    fi
done