#!/bin/bash
set -e
WY_HTTPS_CERTIFICATE_FILE="${WY_HTTPS_CERTIFICATE_FILE:=/wyden/keystore/tls/tls.crt}"
WY_HTTPS_CERTIFICATE_KEY_FILE="${WY_HTTPS_CERTIFICATE_KEY_FILE:=/wyden/keystore/tls/tls.key}"
WY_HTTPS_KEY_STORE_TYPE="${WY_HTTPS_KEY_STORE_TYPE:=PKCS12}"
WY_HTTPS_KEY_STORE_PASSWORD="${WY_HTTPS_KEY_STORE_PASSWORD:=password}"
WY_HTTPS_KEY_STORE_ALIAS="${WY_HTTPS_KEY_STORE_ALIAS:=wyden}"
WY_HTTPS_KEY_STORE_FILE="${WY_HTTPS_KEY_STORE_FILE:=/wyden/keystore/keystore.jks}"
WY_TLS_ENABLED="${WY_TLS_ENABLED:=false}"
WY_TLS_SCAN_ENABLED="${WY_TLS_SCAN_ENABLED:=true}"
WY_TLS_SCAN_INTERVAL="${WY_TLS_SCAN_INTERVAL:=1h}"

logger() {
    local TIMESTAMP=$(date +"%Y-%m-%dT%H:%M:%S.%3NZ")
    local LEVEL="$1"
    local MESSAGE="$2"
    local LOGGER="$3"
    echo '{"@timestamp":"'"$TIMESTAMP"'","level":"'"$LEVEL"'","message":"'"$MESSAGE"'","logger_name":"'"$LOGGER"'"}'
}

generate_keystore() {
    logger "INFO" "Generating keystore using $WY_HTTPS_CERTIFICATE_FILE and $WY_HTTPS_CERTIFICATE_KEY_FILE" "docker.entrypoint.generate_keystore"
    openssl pkcs12 -export -in "$WY_HTTPS_CERTIFICATE_FILE" -inkey "$WY_HTTPS_CERTIFICATE_KEY_FILE" -out /tmp/tls.p12 -passout pass:password -name "$WY_HTTPS_KEY_STORE_ALIAS"
    keytool -importkeystore -deststorepass "$WY_HTTPS_KEY_STORE_PASSWORD" -alias "$WY_HTTPS_KEY_STORE_ALIAS" -srcstoretype PKCS12 -srckeystore /tmp/tls.p12 -srcstorepass password -destkeystore "$WY_HTTPS_KEY_STORE_FILE" -deststoretype "$WY_HTTPS_KEY_STORE_TYPE"
    logger "INFO" "The keystore is available at $WY_HTTPS_KEY_STORE_FILE" "docker.entrypoint.generate_keystore"
    ls -l "$WY_HTTPS_KEY_STORE_FILE"
}


scan_tls() {
    logger "INFO" "Starting the scan TLS certificates changes" "docker.entrypoint.scan_tls"
    MD5_CERTIFICATE_FILE_ORIGIN=$(md5sum "$WY_HTTPS_CERTIFICATE_FILE" | awk '{print $1}')
    MD5_CERTIFICATE_KEY_FILE_ORIGIN=$(md5sum "$WY_HTTPS_CERTIFICATE_KEY_FILE" | awk '{print $1}')
    while true
    do
        MD5_CERTIFICATE_FILE_CURRENT=$(md5sum "$WY_HTTPS_CERTIFICATE_FILE" | awk '{print $1}')
        MD5_CERTIFICATE_KEY_FILE_CURRENT=$(md5sum "$WY_HTTPS_CERTIFICATE_KEY_FILE" | awk '{print $1}')
        if [[ "$MD5_CERTIFICATE_FILE_CURRENT" != "$MD5_CERTIFICATE_FILE_ORIGIN" || "$MD5_CERTIFICATE_KEY_FILE_CURRENT" != "$MD5_CERTIFICATE_KEY_FILE_ORIGIN" ]]
        then
            logger "INFO" "Detected the change in the TLS files" "docker.entrypoint.scan_tls"
            logger "WARN" "Sending SIGHUP signal to the main process" "docker.entrypoint.scan_tls"
            kill -SIGHUP 1
        fi
        sleep "$WY_TLS_SCAN_INTERVAL"
    done
}

if $WY_TLS_ENABLED
then
    generate_keystore
    if $WY_TLS_SCAN_ENABLED
    then
        scan_tls &
    fi
fi

# Start the Java application as PID 1
logger "INFO" "Starting the application" "docker.entrypoint.main"
exec java ${JAVA_OPTS} -javaagent:/wyden/pyroscope.jar -jar /wyden/app.jar "$@"
