---
status: ACCEPTED
---

# Storage implementation for PNL Service

## Context and Problem Statement

We need to choose data storage solution that will allow us to implement all
currently known requirements in PNL Service domain which includes storing finished
trades and performing queries based on multiple different parameters.

Additional remarks:

* Typical query scenario would be calculating exposure on given asset of given client, with input trades filtered by simple criteria
* We will have to use different attributes (or sets of attributes) to filter out trades for computing (SQLs "WHERE" equivalent) - e.g. portfolioName
  and portfolio tags (region, client-tier, risk-profile, etc.)
* We don't expect the data structure to be relational or having to do multiple joins between different measurements or external data sources
* The only query that may require a join at some point is calculation of a ProfitAndLoss at given historical point in time. Since InfluxDB allows to
  perform simple joins across multiple measurements, we should be able to provide such functionality in the future if historical market data was also
  stored as another measurement in InfluxDB
* Trades that will be stored should resemble a transaction log, or a ledger - it will serve as a single source of truth regarding all transactions
  that happened in the system. Given that, it is important that the records should be: immutable, append-only and sorted by timestamp. Ideally,
  corrective transactions, if required, should be explicitly added at the top of the transaction stack as an explicit corrective transaction (instead
  of mutating previous data - this should never happen). We could add service logic later that would apply those records in the background, before
  transmitting to the presentation layer, so corrective transactions would not be visible to end users. The goal is to preserve full audit log in the
  PNL database as a single source of truth.

## Considered Options

* Different types of databases

## Decision Outcome

Our decision is to continue with the combination of Hazelcast + InfluxDB.
We explored different options and we found out that InfluxDB
is heavily used in PNL domain in the old AT and it successfully fulfills
its responsibilities. Some of the pros are:
* a lot of knowledge about Influx within the company
* ability to query the data in the way we need it
* can be used as a data source in Grafana
* acceptable performance
* time series optimization

We discussed possibilities of saving snapshots of computed ProfitAndLoss and other metrics for various portfolios and portfolio queries at given
cut-off point in time. This should allow us to greatly improve performance since we wouldn't have to compute everything from the beginning of the
world every time. Choosing InfluxDB as a database doesn't prevent us from doing that in the future.

Together with it we will use Hazelcast as it provides great performance
when it comes to in-memory computing, storage of temporary data and scalability.