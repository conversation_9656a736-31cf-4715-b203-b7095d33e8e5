---
status: ACCEPTED
---

# Use CQRS approach in Reference data context

## Context and Problem Statement

Creation of reference data and retrieval is often done by two different actors or processes and different data flows and formats are used. 
For example:
* Street side security list is defined by external venues, our system has no control over it
* Sell side security list is defined by financial institutions that use <PERSON>yden as a system for agency trading - we can influence the model but the content is under control of the user
* Sell side security list should be delivered to retail clients (via UI or FIX), as they were described by bank actors
* There's a connection between street and sell side securities that should be dynamic and context-specific. E.g. auto hedging services may pick Street securities for hedging Sell side positions
* There may be more complex business logic in place when it comes to sell-side Security creation. By creating a new sell-side security we are actually opening a trading possibility for retail clients to action the new OrderBook  

## Considered Options

* Simple CRUD
* CQRS

## Decision Outcome

We decided to use CQRS for reference data Security creation and query processes.
Details:
* Street side Security will be created as an outcome of Connector reference data retrieval process. This process will be triggered cyclically by reference data service.
* Sell side Security creation will be initiated by an authorized user via UI. UI will be responsible for sending a Security creation request to REST API Server.
  * Security creation request will be emitted as an event to RabbitMQ
  * Reference data service is responsible for listening for such events
    * Upon receiving such event, Reference data service should register new Security in the system (provided all constraint and validity checks passes)
    * On failure or reject, a Security creation rejection event should be emitted to RabbitMQ
      * It is responsibility of REST API server to catch such event and synchronize entire operation for end-user -> An error should be returned to UI
    * On success, a Security creation event should be emitted to RabbitMQ.
      * One of the consumers is REST API server - such event should be used to synchronize the Security creation process for the user and return a response to UI
      * Another consumers could be other subsystems in the Wyden cloud - SOR, ExecutionEngine, etc.
* As an optimization step, we agreed that REST API server can receive its own storage and store a view of all currently active Securities in its own storage (DB or cache)
  * Storage can be updated whenever new Security creation event arrives
  * On cold start, when storage is empty, it is OK to communicate with Reference data service via REST directly and request current Security list
    * This is OK since Reference data service is a golden source of this data in our system
    * REST request can be load balanced via Kubernetes if there are more Reference data services deployed - single round robin strategy is OK for this purpose
* Sell-side Security deletion process should go via the same path as creation process
  * Should be initiated by an authorized user via UI
  * An event should reach Reference data service with a request to delete Security
  * Deletion should always be a soft-delete process. We can never remove data, especially reference data since it will be used as a relation point for Order Transaction data
