
# Current situation

This is the current situation:

```plantuml
agent "StreetVenue\n//Kraken//" as Venue1
agent "ClientVenue\n//Bank//" as Venue2
agent "ClobVenue\n//Wyden Exchange//" as Venue3
agent Venue<PERSON><PERSON>unt as VenueAccount1
agent VenueA<PERSON>unt as VenueAccount3
agent Wallet as Wallet1
component Instruments as Instruments1
component Instruments as Instruments2
component Instruments as Instruments3
process "Connector Image" as ConnectorImage1
database "Connector Settings\n//ApiKey, ApiSecret//" as ConnectorSettings1

Venue1 -- VenueAccount1
Venue3 -- VenueAccount3
Venue1 - Instruments1
Venue2 - Instruments2
Venue3 - Instruments3
ConnectorImage1 - Venue1
VenueAccount1 -[hidden] Wallet1
VenueAccount1 - ConnectorSettings1
```

1. Venue name determines both the connector image and the instruments. We cannot have different
   instruments provided by the same connector, which poses a problem for Generic connector.
2. Connector settings are associated with the VenueAccount
3. We have:
   * VenueAccounts without Venues (wallets),
   * Venues without VenueAccounts (client venues),
   * VenueAccounts without settings (clob, wallets)
   
   This results in numerous special cases that need to be considered.

# Refactoring - trading accounts

The idea is to separate the VenueAccount and the Connector responsibility:

```plantuml
agent "StreetVenue\n//Kraken//" as Venue1
agent VenueAccount as VenueAccount1
agent Connector as Connector1
component Instruments as Instruments1
process "Connector Image" as ConnectorImage1
database "Connector Settings\n//ApiKey, ApiSecret//" as ConnectorSettings1

Venue1 "1" -- "0..*" VenueAccount1
Venue1 "1" - "0..*" Instruments1 : \t
VenueAccount1 "0..1" -- "0..1" Connector1
Connector1 "0..*" -- "1" ConnectorImage1
Connector1 "1" -- "1" ConnectorSettings1
```

1. A Venue is a container for Instruments and can be created as needed.
2. A new VenueAccount can be attached to an existing Venue or create a new one. This provides
   more flexibility regarding the set of instrument on each VenueAccount.
3. A VenueAccount represents a single Exchange on a Connector (full support for multi-exchange
   connectors is out of scope)
4. A Connector is created independently and can be linked to a VenueAccount.
5. The Connector type (Image) is selected when creating the connector and is not tied to any specific Venue.

# Custody accounts

Custody accounts do not link the entire account to a connector, but instead introduce an Position
object (e.g. BTC, USD, DOGE below).

```plantuml
agent "StreetVenue\n//Kraken//" as Venue1
agent VenueAccount as VenueAccount1
agent "Connector\n//Fireblocks//" as Connector1
agent "Connector\n//Copper//" as Connector2
agent "Connector\n//Kraken//" as ConnectorTrading
agent "Position\n//BTC//" as BTC1
agent "Position\n//USD//" as USD1
agent "Position\n//DOGE//" as DOGE1
agent "Wallet\n//DOGE//" as DOGE2
agent "Wallet\n//BTC//" as BTC2
agent "Wallet\n//USD//" as USD2
component Instruments as Instruments1
process "Connector Image" as ConnectorImage1
process "Connector Image" as ConnectorImage2
process "Connector Image" as ConnectorImageTrading
database "Connector Settings\n//ApiKey, ApiSecret//" as ConnectorSettings1
database "Connector Settings\n//ApiKey, ApiSecret//" as ConnectorSettings2
database "Connector Settings\n//ApiKey, ApiSecret//" as ConnectorSettingsTrading

Venue1 "1..*" -- "0..*" VenueAccount1
Venue1 "1" - "0..*" Instruments1
VenueAccount1 -- ConnectorTrading
VenueAccount1 - BTC1 : \t\t\t\t\t
BTC1 - USD1 : \t
USD1 - DOGE1 : \t\t\t
USD1 -- USD2
BTC1 -- BTC2
DOGE1 -- DOGE2
USD2 -- Connector1
BTC2 -- Connector1
DOGE2 -- Connector2
BTC2 -[hidden] USD2 : \t
USD2 -[hidden] DOGE2 : \t\t\t
Connector1 "0..*" -- "1" ConnectorImage1
Connector1 "1" -- "1" ConnectorSettings1
Connector2 "0..*" -- "1" ConnectorImage2
Connector2 "1" -- "1" ConnectorSettings2
ConnectorTrading -- ConnectorImageTrading
ConnectorTrading -- ConnectorSettingsTrading
```

1. Custody accounts can be either independent or act as trading accounts. In the latter case,
   they link to both trading connector (via account) and custody connectors (via balances)
2. We will fetch list of all available wallets via the connector and present linking options in drop-down list
3. Transfer will involve identifying the source and target wallet and connector (VenueAccount -> Position -> Connector),
   and sending a transfer request to the source connector (note: not all transfers will be supported)
4. Since Kraken wallets are connected with Fireblocks custody connector,
   we can use Fireblocks integration to transfer funds (we are transferring between
   native Fireblocks vault and mapped Kraken account)

# Basic scenario

1. Add the connector "kraken-connector-1" of type Kraken.
2. Add the connector "fireblocks-1" of type Fireblocks.
3. Add the VenueAccount "kraken-1" linked to "kraken-connector-1", with Venue "kraken" (can be selected or newly created), and wallets "BTC" (with link to fireblocks-1 and the mirror wallet address) and "USD" (also with link to fireblocks-1 and mirror wallet address) — this will likely be one screen.
4. Add the VenueAccount "custody-1" with no link, no venue, and Positions "BTC" (with link to fireblocks-1 and the vault wallet address) and "USD" (same setup).
5. Execute an order on "kraken-1".
6. In settlement, define "custody-1" as the target for the VenueAccount "kraken-1".
7. Trigger the settlement.
8. The settlement submits transfer requests to the "fireblocks-1" connector between the vault wallet and the mirror wallet.
