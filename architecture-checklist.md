# Architecture Checklist

The checklist below should be consulted before introducing any new technology product into AT stack or making other architecture decisions.

While there's room for exceptions, exceptional cases should be:
* Consulted first. When in doubt, ask @Maciej, @Bartosz, or @Andy
* Documented as ADR (include reasoning, trade-offs, etc.)

## Checklist

### Check the license

- Only use free to use and free to distribute products (including for commercial use) as dependencies, technologies, solutions. E.g. at least linking and distributing to third parties should be free of charge
- No [copyleft] licensed products (Copyleft means that the product can be used only if the product that uses it will be also distributed on the same licensing terms as the linked product). 
- This for example excludes all [GNU GPL] licensed products 
- If not sure what which license means, take a look at [comparison 1] , [comparison 2]

### Docker Build architecture
- If introducing a new component, or base image for docker builds, make sure all components are supporting at least the following two architectures:
- linux/amd64: Linux x64
- linux/arm64: ARM 64

### Technology replacement 
- A decision to replace a technology in current stack to a different one should be backed by a strong reason why such technology is a better fit. 
- Do not replace technologies for substantial reasons, since we might have already built up a lot of expertise with the existing technology. 
- Important technology replacement decisions should be informed first to @<PERSON> Flury and @Bartosz Wójcik

### Be as technology agnostic as reasonably possible.
- Avoid vendor-locking, or having the overall architecture tied to a single framework or paradigm. 
- Use open standards for communication / integration. 
- When implementing services, keep the business-value code agnostic to frameworks or integration points (aim into storing the business logic in a dedicated layer / sub-component, write ports and adapters for interacting with technology-specific code)
- Stick to industry standards wherever possible. 
- Custom solutions for common problems should be justified and used sparingly.

### Stateless services. 
- Introducing state into services directly should be avoided in distributed environment. 
- This either limits system's scalability and DR capabilities or introduces unnecessary complexity to support those capabilities later.

### Treat servers (and Kubernetes clusters, services, pods) as cattle, not pets.
- Having to manage individual [snowflake server]s kills scalability and continuous integration practices.
- Any service should be OK to kill without warning or manual intervention beforehand (hence, it should not hold any state that is not replicated somewhere else)
- Design services with scalability in mind - deploying more services of the same type should not only not introduce regression, it should - in general - result in increased performance (re responsiveness or throughput)
- It should not really matter in which data center the service is deployed as long as it is discoverable by other systems in the stack (of course, co-locating services will be generally beneficial and a good practice, but deploying in a different data center should not result in limited functionality - e.g. for DR purposes)


## Known and agreed exceptions

### Connector instances (wrapped with a runtime) 
- Connector instances (wrapped with a runtime) are generally considered to be non-scalable, so the "treat services as kettle" rule can be lifted here. 
- We can only have a single (running) instance of the Connector serving a particular client account.
In exchange, we are going to monitor Connector instances health continuously via a dedicated Health-checking mechanism (and automatically re-instantiate if detected to be non-running)
If speed of recovery is identified as an issue later on, we will introduce an active-passive solution for scaling (e.g. having multiple Connectors of the same account deployed, but only one will be connected to the Exchange and actively serving requests)
Any state that could have been lost when Connector instance is killed, should be retrievable via reconciliation mechanisms

[//]: # (These are reference links used in the body of this note and get stripped out when the markdown processor does its job. There is no need to format nicely because it shouldn't be seen. Thanks SO - http://stackoverflow.com/questions/4823468/store-comments-in-markdown-syntax)

[copyleft]: <https://en.wikipedia.org/wiki/Copyleft>
[GNU GPL]: <https://en.wikipedia.org/wiki/GNU_General_Public_License>
[comparison 1]: <https://en.wikipedia.org/wiki/Comparison_of_free_and_open-source_software_licences>
[comparison 2]: <https://choosealicense.com/licenses/>
[snowflake server]: <https://martinfowler.com/bliki/SnowflakeServer.html>