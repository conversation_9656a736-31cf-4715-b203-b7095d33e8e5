#!/bin/bash

set -e

usage() {
  echo "Usage: $0 [-p bitstamp] [-d oracle_mac]"
  echo
  echo "Start whole environment."
  echo
  echo "Options:"
  echo "  -h    Display help"
  echo "  -p    Custom profiles to start: all,bitstamp or e2e_test"
  echo "  -d    Used database, default is postgres other options: oracle, oracle_mac"
}

PROFILES=all
DATABASE=postgres
while getopts "hp:d:" o; do
  case "${o}" in
    h)
      usage
      exit 0
      ;;
    p)
      PROFILES="${OPTARG}"
      ;;
    d)
      DATABASE="${OPTARG}"
      ;;
    :)
      echo "Error: -${OPTARG} requires and argument."
      usage
      exit 255
      ;;
    *)
      usage
      exit 255
      ;;
  esac
done

echo "Starting database: ${DATABASE}"

if [[ $DATABASE = "oracle" ]] ; then
  OVERRIDES="-f docker-compose-oracle.yml"
elif [[ $DATABASE = "oracle_mac" ]] ; then
  OVERRIDES="-f docker-compose-oracle.yml -f docker-compose-oracle-mac.yml"
else
  OVERRIDES=""
fi

echo "Starting all containers for profiles: ${PROFILES}"
COMPOSE_PROFILES="${PROFILES}" docker compose -f docker-compose.yml ${OVERRIDES} up -d
