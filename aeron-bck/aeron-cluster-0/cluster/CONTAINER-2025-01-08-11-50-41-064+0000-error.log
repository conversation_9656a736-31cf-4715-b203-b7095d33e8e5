
1 observations from 2024-12-12 19:12:22.286+0000 to 2024-12-12 19:12:22.286+0000 for:
io.aeron.exceptions.ConductorServiceTimeoutException: FATAL - service interval exceeded: timeout=3600000000000ns, interval=7945748261816ns
	at io.aeron.ClientConductor.checkServiceInterval(ClientConductor.java:1577)
	at io.aeron.ClientConductor.checkTimeouts(ClientConductor.java:1561)
	at io.aeron.ClientConductor.service(ClientConductor.java:1470)
	at io.aeron.ClientConductor.doWork(ClientConductor.java:196)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2024-12-12 19:12:22.295+0000 to 2024-12-12 19:12:22.295+0000 for:
io.aeron.cluster.client.ClusterException: ERROR - publication is closed
	at io.aeron.cluster.service.ConsensusModuleProxy.checkResult(ConsensusModuleProxy.java:227)
	at io.aeron.cluster.service.ConsensusModuleProxy.scheduleTimer(ConsensusModuleProxy.java:77)
	at io.aeron.cluster.service.ClusteredServiceAgent.scheduleTimer(ClusteredServiceAgent.java:351)
	at io.wyden.aeron.node.realtime.RealtimeTimerManager.scheduleAeronTimer(RealtimeTimerManager.java:43)
	at io.wyden.aeron.node.AeronTimerManager.scheduleTimer(AeronTimerManager.java:62)
	at io.wyden.aeron.node.realtime.RealtimeTimerManager.scheduleTimer(RealtimeTimerManager.java:30)
	at io.wyden.aeron.node.AeronTimerManager.onTimerEvent(AeronTimerManager.java:109)
	at io.wyden.aeron.node.ExchangeCoreStateMachine.onTimerEvent(ExchangeCoreStateMachine.java:112)
	at io.wyden.aeron.node.AeronClusteredService.onTimerEvent(AeronClusteredService.java:113)
	at io.aeron.cluster.service.ClusteredServiceAgent.onTimerEvent(ClusteredServiceAgent.java:480)
	at io.aeron.cluster.service.BoundedLogAdapter.onMessage(BoundedLogAdapter.java:177)
	at io.aeron.cluster.service.BoundedLogAdapter.onFragment(BoundedLogAdapter.java:70)
	at io.aeron.Image.boundedControlledPoll(Image.java:557)
	at io.aeron.cluster.service.BoundedLogAdapter.poll(BoundedLogAdapter.java:133)
	at io.aeron.cluster.service.ClusteredServiceAgent.doWork(ClusteredServiceAgent.java:242)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2024-12-12 19:12:22.344+0000 to 2024-12-12 19:12:22.344+0000 for:
org.agrona.concurrent.AgentTerminationException
	at io.aeron.ClientConductor.doWork(ClientConductor.java:193)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2024-12-12 19:12:22.345+0000 to 2024-12-12 19:12:22.345+0000 for:
org.agrona.concurrent.AgentTerminationException: unexpected Aeron close
	at io.aeron.cluster.service.ClusteredServiceAgent.checkForClockTick(ClusteredServiceAgent.java:1070)
	at io.aeron.cluster.service.ClusteredServiceAgent.doWork(ClusteredServiceAgent.java:235)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


4 distinct errors observed.
