
5 observations from 2024-12-12 16:11:35.273+0000 to 2024-12-12 16:12:04.426+0000 for:
java.lang.RuntimeException: java.lang.RuntimeException: error parsing CronExpression
	at io.wyden.aeron.node.realtime.RealtimeFragmentHandler.onFragment(RealtimeFragmentHandler.java:209)
	at io.wyden.aeron.node.AeronClusteredService.onSessionMessage(AeronClusteredService.java:108)
	at io.aeron.cluster.service.ClusteredServiceAgent.onSessionMessage(ClusteredServiceAgent.java:473)
	at io.aeron.cluster.service.BoundedLogAdapter.onMessage(BoundedLogAdapter.java:156)
	at io.aeron.cluster.service.BoundedLogAdapter.onFragment(BoundedLogAdapter.java:70)
	at io.aeron.Image.boundedControlledPoll(Image.java:557)
	at io.aeron.cluster.service.BoundedLogAdapter.poll(BoundedLogAdapter.java:133)
	at io.aeron.cluster.service.ClusteredServiceAgent.doWork(ClusteredServiceAgent.java:242)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.lang.RuntimeException: error parsing CronExpression
	at io.wyden.aeron.node.timer.RecurringTask.init(RecurringTask.java:17)
	at io.wyden.aeron.node.ExchangeCoreStateMachine.scheduleOrderBookTimer(ExchangeCoreStateMachine.java:294)
	at io.wyden.aeron.node.realtime.RealtimeFragmentHandler.onFragment(RealtimeFragmentHandler.java:173)
	... 11 more
Caused by: java.text.ParseException: '?' can only be specfied for Day-of-Month or Day-of-Week.
	at io.wyden.aeron.node.timer.CronExpression.storeExpressionVals(CronExpression.java:599)
	at io.wyden.aeron.node.timer.CronExpression.buildExpression(CronExpression.java:479)
	at io.wyden.aeron.node.timer.CronExpression.<init>(CronExpression.java:268)
	at io.wyden.aeron.node.timer.RecurringTask.init(RecurringTask.java:15)
	... 13 more


1 distinct errors observed.
