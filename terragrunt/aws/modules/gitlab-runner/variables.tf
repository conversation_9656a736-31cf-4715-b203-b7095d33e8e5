variable "values_path" {
  description = "Path of value.yaml for gitlab-runner"
  type        = string
}

variable "secrets" {
  description = "Gitlab token Secrets value"
  type        = map(string)
  default     = {}
}

variable "cluster_name" {
  description = "EKS cluster name"
  type        = string
}

variable "runner_name" {
  type        = string
  description = "Gitlab runner name"
  default     = "gitlab-runner"
}

variable "gitlab_runner_version" {
  description = "Gitlab runner chart version"
  type        = string
}

variable "gitlab_url" {
  description = "Gitlab url"
  type        = string
  default     = "https://gitlab.com"
}

variable "namespace" {
  description = "Gitlab runner namespace"
  type        = string
  default     = "gitlab-runner"
}

variable "tags" {
  type        = map(string)
  description = "A mapping of tags to assign to the resource."
  default     = {}
}

variable "role_policy_arns" {
  description = "ARNs of any policies to attach to the IAM role"
  type        = map(string)
  default     = {}
}
