data "aws_subnet" "target" {
  for_each = toset(var.subnet_ids)
  id       = each.value
}

locals {
  cidr_blocks = [for s in data.aws_subnet.target : s.cidr_block]
}

module "eks" {
  source  = "terraform-aws-modules/eks/aws"
  version = "20.31.6"

  cluster_name                    = var.cluster_name
  cluster_version                 = var.cluster_version
  cluster_endpoint_private_access = true
  cluster_endpoint_public_access  = true
  kms_key_enable_default_policy   = false

  vpc_id     = var.vpc_id
  subnet_ids = var.subnet_ids

  enable_irsa = true

  cluster_timeouts = {
    create = "30m"
    delete = "15m"
  }

  cluster_enabled_log_types = []

  create_cluster_security_group          = true
  cluster_security_group_use_name_prefix = true
  # Extend cluster security group rules
  cluster_security_group_additional_rules = {
    egress_nodes_ephemeral_ports_tcp = {
      description                = "To node 1025-65535"
      protocol                   = "tcp"
      from_port                  = 1025
      to_port                    = 65535
      type                       = "egress"
      source_node_security_group = true
    }
    ingress_nodes_karpenter_ports_tcp = {
      description                = "Karpenter readiness"
      protocol                   = "tcp"
      from_port                  = 8443
      to_port                    = 8443
      type                       = "ingress"
      source_node_security_group = true
    }
  }

  create_node_security_group = true
  node_security_group_additional_rules = {
    ingress_self_all = {
      description = "Node to node all ports/protocols"
      protocol    = "-1"
      from_port   = 0
      to_port     = 0
      type        = "ingress"
      ## do not use self=true because it won't work for Fargate
      cidr_blocks = local.cidr_blocks
    }
    # Recommended outbound traffic for Node groups
    egress_all = {
      description      = "Node all egress"
      protocol         = "-1"
      from_port        = 0
      to_port          = 0
      type             = "egress"
      cidr_blocks      = ["0.0.0.0/0"]
      ipv6_cidr_blocks = ["::/0"]
    }
  }

  node_security_group_tags = {
    "karpenter.sh/discovery" = var.cluster_name
  }

  cluster_addons = {
    coredns = {
      most_recent                 = true
      preserve                    = true
      resolve_conflicts_on_update = "OVERWRITE"
      configuration_values = jsonencode({
        computeType = "Fargate"
        autoScaling = {
          enabled     = true
          minReplicas = 3
          maxReplicas = 10
        }
        resources = {
          limits = {
            cpu    = "0.25"
            memory = "256M"
          }
          requests = {
            cpu    = "0.25"
            memory = "256M"
          }
        }
      })
    }
    kube-proxy = {
      # set same version during cluster update, upgrade later with second run
      # addon_version               = "v1.30.3-eksbuild.2"
      most_recent                 = true
      preserve                    = true
      resolve_conflicts_on_create = "OVERWRITE"
      resolve_conflicts_on_update = "OVERWRITE"
    }
    vpc-cni = {
      # set same version during cluster update, upgrade later with second run
      # addon_version               = "v1.18.3-eksbuild.2"
      most_recent                 = true
      before_compute              = true
      preserve                    = true
      resolve_conflicts_on_create = "OVERWRITE"
      resolve_conflicts_on_update = "OVERWRITE"
      configuration_values = jsonencode({
        env = {
          # Reference docs https://docs.aws.amazon.com/eks/latest/userguide/cni-increase-ip-addresses.html
          ENABLE_PREFIX_DELEGATION = "true"
          WARM_PREFIX_TARGET       = "1"
        }
      })
      service_account_role_arn = module.vpc_cni_ipv4_irsa_role.iam_role_arn
    }
    aws-ebs-csi-driver = {
      # set same version during cluster update, upgrade later with second run
      # addon_version               = "v1.34.0-eksbuild.1"
      most_recent                 = true
      preserve                    = true
      resolve_conflicts_on_update = "OVERWRITE"
      service_account_role_arn    = module.ebs_csi_driver_irsa.iam_role_arn
    }
    eks-pod-identity-agent = {
      most_recent                 = true
      preserve                    = true
      resolve_conflicts_on_update = "OVERWRITE"
    }
  }

  eks_managed_node_group_defaults = {
    ami_type = "BOTTLEROCKET_ARM_64"
    # use_custom_launch_template = false
    disk_size     = 40
    ebs_optimized = true
  }

  eks_managed_node_groups = var.eks_managed_node_groups

  enable_cluster_creator_admin_permissions = true

  access_entries = var.access_entries

  # fargate_profile_defaults = {
  #   iam_role_additional_policies = {
  #     # Required by https://docs.aws.amazon.com/eks/latest/userguide/security-groups-for-pods.html
  #     additional = "arn:aws:iam::aws:policy/AmazonEKSVPCResourceController",
  #     # Required by Karpenter
  #     karpenter = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
  #     # Required by CSI Driver
  #     csi = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
  #   }
  # }

  fargate_profiles = {
    karpenter = {
      subnet_ids = var.subnet_ids
      selectors = [
        { namespace = "karpenter" }
      ]
    }
    kube_system = {
      name       = "kube-system"
      subnet_ids = var.subnet_ids
      selectors = [
        { namespace = "kube-system" }
      ]
    }
  }

  tags = var.tags
}

module "ebs_csi_driver_irsa" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "5.48.0"

  role_name_prefix      = "${module.eks.cluster_name}-ebs-csi-driver-"
  attach_ebs_csi_policy = true
  # role_policy_arns      = [""]

  oidc_providers = {
    main = {
      provider_arn               = module.eks.oidc_provider_arn
      namespace_service_accounts = ["kube-system:ebs-csi-controller-sa"]
    }
  }

  tags = var.tags
}

module "vpc_cni_ipv4_irsa_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "5.48.0"

  role_name             = "${module.eks.cluster_name}-vpc-cni-ipv4"
  attach_vpc_cni_policy = true
  vpc_cni_enable_ipv4   = true

  oidc_providers = {
    ex = {
      provider_arn               = module.eks.oidc_provider_arn
      namespace_service_accounts = ["kube-system:aws-node"]
    }
  }

  tags = var.tags
}

################################################################################
# Storage Classes
################################################################################

resource "kubernetes_annotations" "gp2" {
  api_version = "storage.k8s.io/v1"
  kind        = "StorageClass"
  force       = "true"

  metadata {
    name = "gp2"
  }

  annotations = {
    # Modify annotations to remove gp2 as default storage class still reatain the class
    "storageclass.kubernetes.io/is-default-class" = "false"
  }
}

resource "kubernetes_storage_class_v1" "gp3" {
  metadata {
    name = "gp3"

    annotations = {
      # Annotation to set gp3 as default storage class
      "storageclass.kubernetes.io/is-default-class" = "true"
    }
  }

  storage_provisioner    = "ebs.csi.aws.com"
  allow_volume_expansion = true
  reclaim_policy         = "Delete"
  volume_binding_mode    = "WaitForFirstConsumer"

  parameters = {
    encrypted = true
    fsType    = "ext4"
    type      = "gp3"
  }
}

resource "aws_security_group_rule" "all-tcp-udp" {
  security_group_id = module.eks.cluster_primary_security_group_id
  description       = "Access tcp-udp internally"
  protocol          = -1
  from_port         = 0
  to_port           = 0
  type              = "ingress"
  cidr_blocks       = local.cidr_blocks
}
