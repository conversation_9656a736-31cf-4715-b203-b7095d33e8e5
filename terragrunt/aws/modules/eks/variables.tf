variable "cluster_name" {
  description = "Name of the EKS cluster."
  type        = string
}

variable "cluster_version" {
  description = "Version of the EKS cluster."
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC where the EKS cluster will be deployed."
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for the EKS cluster."
  type        = list(string)
}

variable "access_entries" {
  description = "IAM access configuration for the EKS cluster."
  type = map(object({
    kubernetes_groups = list(string)
    principal_arn     = string
    policy_associations = map(object({
      policy_arn = string
      access_scope = object({
        namespaces = optional(list(string), null)
        type       = string
      })
    }))
  }))
  default = {
    "name" = {
      "kubernetes_groups" = ["*"]
      "principal_arn"     = "arn:aws:iam::012345678901:user/test"
      "policy_associations" = {
        "cluster-admin" = {
          "policy_arn" = "arn:aws:iam::012345678901:policy/eks-admin"
          "access_scope" = {
            "type" = "cluster"
          }
        }
      }
    }
  }
}

variable "eks_managed_node_groups" {
  description = "Map of EKS managed node groups configuration."
  type = map(object({
    name            = string
    use_name_prefix = bool

    desired_size = number
    max_size     = number
    min_size     = number
    update_config = object({
      max_unavailable = number
    })
    iam_role_additional_policies = map(string)
    instance_types               = list(string)
    capacity_type                = string
    subnet_ids                   = list(string)
    tags                         = map(string)

    labels = map(string)
  }))
}

variable "tags" {
  description = "Tags to apply to all resources created by the module."
  type        = map(string)
  default     = {}
}

variable "node_pools_path" {
  description = "Path to the directory containing YAML files for node_pools."
  type        = string
}

variable "node_templates_path" {
  description = "Path to the directory containing YAML files for node templates."
  type        = string
  default     = "value"
}
