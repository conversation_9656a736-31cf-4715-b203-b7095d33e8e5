terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-kms.git?ref=${include.root.locals.terraform-aws-kms}"
}

## Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}
## terragrunt import 'aws_kms_alias.this["vault/wyden-uat"]' alias/vault/wyden-uat
## terragrunt import 'aws_kms_key.this[0]' 6420b34f-1e2e-4a49-bb4b-9974a901db3f
inputs = {
  aliases = ["vault/${include.root.locals.merged.default_tags.client}-${include.root.locals.merged.default_tags.environment}"]
  description                        = "Using for vault autounseal for vault in ${include.root.locals.merged.default_tags.environment} environment"
  deletion_window_in_days            = 30
  enable_key_rotation                = false
  multi_region                       = false
  bypass_policy_lockout_safety_check = false
  customer_master_key_spec           = "SYMMETRIC_DEFAULT"
  is_enabled                         = true
  key_usage                          = "ENCRYPT_DECRYPT"
  policy                             = <<EOF
{
    "Version": "2012-10-17",
    "Id": "key-default-1",
    "Statement": [
        {
            "Sid": "Enable IAM User Permissions",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::${include.root.locals.account_id}:root"
            },
            "Action": "kms:*",
            "Resource": "*"
        },
        {
          "Sid": "Allow use of the key",
          "Effect": "Allow",
          "Principal": {
            "AWS": [
              "arn:aws:iam::${include.root.locals.account_id}:role/OrganizationAccountAccessRole",
              "arn:aws:iam::${include.root.locals.account_id}:user/vault"
            ]
          },
          "Action": [
            "kms:Encrypt",
            "kms:Decrypt",
            "kms:ReEncrypt*",
            "kms:GenerateDataKey*",
            "kms:DescribeKey"
          ],
          "Resource": "*"
        }
    ]
}
EOF

  tags = merge(include.root.locals.default_tags)
}
