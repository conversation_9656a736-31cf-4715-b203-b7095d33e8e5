apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: qa
spec:
  role: "${node_iam_role_name}"
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: "*"
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "*"
  tags:
    managed-by: karpenter
    karpenter.sh/discovery: "${cluster_name}"
    Name: "karpenter-qa"
    environment: "qa"
  # supported values: "AL2", "AL2023", "Bottlerocket", "Custom", "Windows2019", "Windows2022"
  amiFamily: Bottlerocket
  amiSelectorTerms:
    # Get list of all available versions:
    # export K8S_VERSION="1.30"
    # aws ssm get-parameters-by-path --path "/aws/service/bottlerocket/aws-k8s-$K8S_VERSION" --recursive | jq -cr '.Parameters[].Name' | grep -v "latest" | awk -F '/' '{print $7}' | sort | uniq
    # aws ssm get-parameters-by-path --path "/aws/service/eks/optimized-ami/$K8S_VERSION/amazon-linux-2023/" --recursive | jq -cr '.Parameters[].Name' | grep -v "recommended" | awk -F '/' '{print $10}' | sed -r 's/.*(v[[:digit:]]+)$/\1/' | sort | uniq
    # aws ssm get-parameters-by-path --path "/aws/service/eks/optimized-ami/$K8S_VERSION/amazon-linux-2/" --recursive | jq -cr '.Parameters[].Name' | grep -v "recommended" | awk -F '/' '{print $10}' | sed -r 's/.*(v[[:digit:]]+)$/\1/' | sort | uniq
    - alias: bottlerocket@v1.26.2
  # Optional, configures storage devices for the instance
  blockDeviceMappings:
    # Root device
    - deviceName: /dev/xvda
      ebs:
        volumeSize: 4Gi
        volumeType: gp3
        encrypted: true
    # Data device: Container resources such as images and logs
    - deviceName: /dev/xvdb
      ebs:
        volumeSize: 40Gi
        volumeType: gp3
        encrypted: true
