apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: qa
spec:
  template:
    metadata:
      labels:
        environment: qa
    spec:
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: qa
      taints:
        - key: qa
          value: "true"
          effect: NoSchedule
      requirements:
        - key: "topology.kubernetes.io/zone"
          operator: In
          values: [${availability_zones}]
        - key: "karpenter.sh/capacity-type"
          operator: In
          values: ["spot", "on-demand"]
        - key: "kubernetes.io/arch"
          operator: In
          values: ["arm64", "amd64"]
        - key: karpenter.k8s.aws/instance-category
          operator: In
          values: ["t"]
      kubelet:
        systemReserved:
          cpu: 100m
          memory: 100Mi
          ephemeral-storage: 1Gi
  limits:
    cpu: 64
    memory: 148Gi
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 30m
    budgets:
    # On Weekdays during business hours, don't do any deprovisioning.
    - schedule: "0 9 * * mon-fri"
      duration: 8h
      nodes: "0"
