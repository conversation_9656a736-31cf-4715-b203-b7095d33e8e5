dependency "vpc" {
  config_path = "${get_parent_terragrunt_dir()}/${include.root.locals.environment}/${include.root.locals.aws_region}/vpc"
}

terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-vpc//modules/vpc-endpoints?ref=${include.root.locals.terraform-aws-vpc}"
}

# Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

inputs = {
  vpc_id = dependency.vpc.outputs.vpc_id
  create_security_group = true
  security_group_name = "ssm-vpc-endpoints"
  security_grou_description = "SSM VPC endpoints"
  security_group_rules = {
    ingress_https_a = {
      description = "HTTPS from VPC AZ a"
      cidr_blocks = [dependency.vpc.outputs.private_subnets_cidr_blocks[0]]
    }
    ingress_https_b = {
      description = "HTTPS from VPC AZ b"
      cidr_blocks = [dependency.vpc.outputs.private_subnets_cidr_blocks[1]]
    }
    ingress_https_c = {
      description = "HTTPS from VPC AZ c"
      cidr_blocks = [dependency.vpc.outputs.private_subnets_cidr_blocks[2]]
    }
  }
  endpoints = {
    ssm = {
      service = "ssm"
      private_dns_enabled = true
      subnet_ids = dependency.vpc.outputs.private_subnets
      tags = {
        Name = "ssm-vpc-endpoint"
      }
    }
    "ssmmessages" = {
      service = "ssmmessages"
      private_dns_enabled = true
      subnet_ids = dependency.vpc.outputs.private_subnets
      tags = {
        Name = "ssmmessages-vpc-endpoint"
      }
    }
    "ec2messages" = {
      service = "ec2messages"
      private_dns_enabled = true
      subnet_ids = dependency.vpc.outputs.private_subnets
      tags = {
        Name = "ec2messages-vpc-endpoint"
      }
    }
  }

  tags = merge(include.root.locals.default_tags)
}
