terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-iam.git//modules/iam-policy?ref=${include.root.locals.terraform-aws-iam}"
}

## Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

dependency "s3_bucket" {
  config_path = "${get_parent_terragrunt_dir("root")}/${include.root.locals.environment}/${include.root.locals.aws_region}/s3/backups"
}

inputs = {
  name                  = "S3Bucket-${dependency.s3_bucket.outputs.s3_bucket_id}-rw"
  create_policy         = true
  description           = "ReadWrite access for bucket ${dependency.s3_bucket.outputs.s3_bucket_id}"
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "${dependency.s3_bucket.outputs.s3_bucket_arn}",
                "${dependency.s3_bucket.outputs.s3_bucket_arn}/*"
            ]
        }
    ]
}
EOF

  tags = merge(include.root.locals.default_tags)
}
