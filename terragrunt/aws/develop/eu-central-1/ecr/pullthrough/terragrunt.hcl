terraform {
  source = "git::https://github.com/opsworks-co/ecr-pull-through.git?ref=${include.root.locals.ecr-pull-through}"
}

# Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

locals {
  registries = yamldecode(file("${get_terragrunt_dir()}/files/secrets.yaml"))
}

inputs = {
  registries = local.registries
  tags       = merge(include.root.locals.default_tags)
}