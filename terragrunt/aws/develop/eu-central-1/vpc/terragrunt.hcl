locals {
  name             = include.root.locals.environment
  cidr             = "172.16.0.0/16"
  subnets          = cidrsubnets(local.cidr, 3, 3, 3, 4, 4, 4, 4, 4, 4, 8, 8, 8)
  private_subnets  = chunklist(local.subnets, 3)[0]
  public_subnets   = chunklist(local.subnets, 3)[1]
  intra_subnets    = chunklist(local.subnets, 3)[2]
  database_subnets = chunklist(local.subnets, 3)[3]
}

terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-vpc//?ref=${include.root.locals.terraform-aws-vpc}"
}

# Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

inputs = {
  name = local.name
  cidr = local.cidr

  create_database_subnet_group = true
  enable_ipv6                  = false
  enable_dns_hostnames         = true
  enable_vpn_gateway           = false
  enable_nat_gateway           = true
  single_nat_gateway           = true
  map_public_ip_on_launch      = true

  azs                 = ["${include.root.locals.aws_region}a", "${include.root.locals.aws_region}b", "${include.root.locals.aws_region}c"]
  private_subnets     = local.private_subnets
  public_subnets      = local.public_subnets
  database_subnets    = local.database_subnets
  intra_subnets       = local.intra_subnets
  elasticache_subnets = []
  redshift_subnets    = []

  public_subnet_tags = {
    "kubernetes.io/role/elb"                                               = 1
    "kubernetes.io/cluster/${include.root.locals.merged.eks.cluster_name}" = "shared"
    "karpenter.sh/discovery"                                               = include.root.locals.merged.eks.cluster_name
  }

  private_subnet_tags = {
    "kubernetes.io/role/internal-elb"                                      = 1
    "kubernetes.io/cluster/${include.root.locals.merged.eks.cluster_name}" = "shared"
    "karpenter.sh/discovery"                                               = include.root.locals.merged.eks.cluster_name
  }

  vpc_tags = {
    Name = local.name
  }

  tags = merge(include.root.locals.default_tags)
}
