# Terragrunt
## Pre-requirements
Installed tools
- Azure CLI
- Terraform
- Terragrunt

Configure Azure CLI to be able to access our account.

## Apply changes
To apply changes to all entities in the environment, change the directory to the environment and execute `run-all apply` command.

Rule of thumb to check the changes before apply with `plan`.

### Example
There is a need to apply changes in the development environment:
```sh
cd terragrunt/dev
terragrunt run-all plan
terragrunt run-all apply
```

If there is no need to apply changes to many items, then go to the item directory and `apply` it directly there.

### Example
```sh
cd terragrunt/dev/westeurope/aks
terragrunt plan
terragrunt apply
```

## Workload identity
To be able to use workload identites in the AKS cluster, there is a need to install Helm application to the cluster.

The in-build workload identity feature doesn't work correctly (e.g. doesn't inject sidecar PODs if the annatotaion is specified in the manifest)

Add repo
```sh
helm repo add azure-workload-identity https://azure.github.io/azure-workload-identity/charts
```

Install application
```sh
helm upgrade --install --namespace azure-workload-identity-system --create-namespace workload-identity-webhook azure-workload-identity/workload-identity-webhook --set azureTenantID=<TENANT_ID>
```