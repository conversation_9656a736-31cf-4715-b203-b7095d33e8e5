locals {
  location = include.root.locals.location
}

dependency "oidc-azure" {
  config_path = "../oidc-azure"
  mock_outputs = {
    managed_identity_name = "test"
    aks_managed_identity_name = "test"
  }
}

dependencies {
  paths = ["../oidc-azure"]
}

terraform {
  source = "${get_parent_terragrunt_dir()}/terragrunt/modules/key-vault"
}

include "root" {
  path = find_in_parent_folders()
  expose = true
}

inputs = {
  resource_group_name                  = "${include.root.locals.resource_group}"
  location = local.location
  key_vault_name             = "wyden-cloud-poc"
  key_vault_sku_pricing_tier = "standard"
  access_policies = [
    {
      azure_ad_user_principal_names = ["anatolii.buh<PERSON><EMAIL>","<EMAIL>"]
      key_permissions         = ["Create", "Delete", "Get", "Backup", "Decrypt", "Encrypt", "Import", "List", "Purge", "Recover", "Restore", "Sign", "Update", "Verify", "GetRotationPolicy", "SetRotationPolicy"]
      secret_permissions      = ["Backup", "Delete", "Get", "List", "Purge", "Recover", "Restore", "Set"]
      certificate_permissions = ["Backup", "Create", "Delete", "DeleteIssuers", "Get", "GetIssuers", "Import", "List", "ListIssuers", "ManageContacts", "ManageIssuers", "Purge", "Recover", "Restore", "SetIssuers", "Update"]
      storage_permissions     = ["Backup", "Delete", "DeleteSAS", "Get", "GetSAS", "List", "ListSAS", "Purge", "Recover", "RegenerateKey", "Restore", "Set", "SetSAS", "Update"]
    },

    {
      azure_ad_service_principal_names = [dependency.oidc-azure.outputs.vault_identity_name,dependency.oidc-azure.outputs.aks_identity_name]
      key_permissions                  = ["Create", "Delete", "Get", "Backup", "Decrypt", "Encrypt", "Import", "List", "Purge", "Recover", "Restore", "Sign", "Update", "Verify", "GetRotationPolicy", "SetRotationPolicy", "WrapKey", "UnwrapKey"]
      secret_permissions               = ["Get", "List"]
      certificate_permissions          = ["Get", "List"]
      storage_permissions              = ["Backup", "Get", "List", "Recover"]
    }
  ]

  soft_delete_retention_days = 7

  tags = {
    Environment = "${include.root.locals.merged.environment}"
  }
}