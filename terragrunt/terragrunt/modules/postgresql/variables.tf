variable "name" {
  description = ""
  type        = string
  default     = "database"
}

variable "resource_group" {
  description = ""
  type        = string
  default     = null
}

variable "location" {
  description = ""
  type        = string
  default     = null
}

variable "postgresql_version" {
  description = ""
  type        = string
  default     = "14"
}

variable "administrator_login" {
  description = ""
  type        = string
  default     = "admin"
}

variable "administrator_password" {
  description = ""
  type        = string
  default     = "admin"
}

variable "subnet_id" {
  description = ""
  type        = string
}

variable "storage" {
  description = ""
  type        = string
  default     = "32768"
}

variable "size" {
  description = ""
  type        = string
  default     = "B_Standard_B2s"
}

variable "tags" {
  type        = map(string)
  description = "Any tags can be set"
  default     = {}
}

variable "db_parameters" {
  type        = map(string)
  description = ""
  default     = {}
}

variable "dns_zone_name" {
  type        = string
  description = "Name for the DNS zone (required for the private Subnet)"
  default     = "test"
}

variable "vnet_id" {
  type        = string
  description = "Virtual network ID where the PostgreSQL will be deployed"
  default     = null
}

variable "public_access" {
  type        = bool
  description = "Expose PostgreSQL instance publically"
  default     = false
}
