package io.wyden.target.registry.k8s;

import org.springframework.stereotype.Component;

@Component
class K8SDeleteService {

    private final K8SApiClient apiClient;

    K8SDeleteService(K8SApiClient apiClient) {
        this.apiClient = apiClient;
    }

    void deleteDeployment(String venueAccount) {
        String labelSelector = K8SConnectorLabelUtils.getLabelSelectorForVenueAccount(venueAccount);
        apiClient.deleteDeployment(venueAccount, labelSelector);
    }
}
