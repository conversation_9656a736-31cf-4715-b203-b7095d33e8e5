{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "limit": 100, "name": "Annotations & Alerts", "showIn": 0, "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "enable": true, "expr": "resets(process_uptime_seconds{namespace=\"$namespace\", wyden_service=\"$wyden_service\"}[1m]) > 0", "iconColor": "rgba(255, 96, 96, 1)", "name": "Restart Detection", "showIn": 0, "step": "1m", "tagKeys": "restart-tag", "textFormat": "uptime reset", "titleFormat": "<PERSON><PERSON>"}]}, "description": "Dashboard for Micrometer instrumented applications (Java, Spring Boot, Micronaut)", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 4701, "graphTooltip": 1, "id": 575, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 125, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "Quick Facts", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 1}, "id": 63, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_uptime_seconds{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "range": true, "refId": "A", "step": 14400}], "title": "Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dateTimeAsIso"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 1}, "id": 92, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {"valueSize": 25}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_start_time_seconds{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}*1000", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "range": true, "refId": "A", "step": 14400}], "title": "Start time", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 1}, "id": 65, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"heap\"})*100/sum(jvm_memory_max_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "range": true, "refId": "A", "step": 14400}], "title": "Heap used", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"from": -1e+32, "result": {"text": "N/A"}, "to": 0}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 1}, "id": 75, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"nonheap\"})*100/sum(jvm_memory_max_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "range": true, "refId": "A", "step": 14400}], "title": "Non-Heap used", "type": "stat"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 126, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "I/O Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 5}, "id": 111, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(rate(http_server_requests_seconds_count{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}[1m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "HTTP", "range": true, "refId": "A"}], "title": "Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "HTTP"}, "properties": [{"id": "color", "value": {"fixedColor": "#890f02", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "HTTP - 5xx"}, "properties": [{"id": "color", "value": {"fixedColor": "#bf1b00", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 5}, "id": 112, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(rate(http_server_requests_seconds_count{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", status=~\"5..\"}[1m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "HTTP - 5xx", "range": true, "refId": "A"}], "title": "Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 5}, "id": 113, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(rate(http_server_requests_seconds_sum{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", status!~\"5..\"}[1m]))/sum(rate(http_server_requests_seconds_count{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", status!~\"5..\"}[1m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "HTTP - AVG", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "max(http_server_requests_seconds_max{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", status!~\"5..\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "HTTP - MAX", "range": true, "refId": "B"}], "title": "Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 5}, "id": 119, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "tomcat_threads_busy_threads{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "TOMCAT - BSY", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "tomcat_threads_current_threads{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "TOMCAT - CUR", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "tomcat_threads_config_max_threads{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "TOMCAT - MAX", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jetty_threads_busy{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - BSY", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jetty_threads_current{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - CUR", "range": true, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jetty_threads_config_max{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - MAX", "range": true, "refId": "F"}], "title": "Utilisation", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 127, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "JVM Memory", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 13}, "id": 24, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_committed_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "range": true, "refId": "B", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_max_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "range": true, "refId": "C", "step": 2400}], "title": "JVM <PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 13}, "id": 25, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"nonheap\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_committed_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "range": true, "refId": "B", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_max_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "range": true, "refId": "C", "step": 2400}], "title": "JVM Non-Heap", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 13}, "id": 26, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_committed_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "range": true, "refId": "B", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(jvm_memory_max_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "range": true, "refId": "C", "step": 2400}], "title": "JVM Total", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 13}, "id": 86, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_memory_vss_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "vss", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_memory_rss_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "rss", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_memory_swap_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "swap", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_memory_rss_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"} + process_memory_swap_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "total", "range": true, "refId": "D"}], "title": "JVM Process Memory", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 128, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "JVM Misc", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 1, "links": [], "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 21}, "id": 106, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "system_cpu_usage{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "system", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_cpu_usage{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "process", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "avg_over_time(process_cpu_usage{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}[1h])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "process-1h", "range": true, "refId": "C"}], "title": "CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 1, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 21}, "id": 93, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "system_load_average_1m{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "system-1m", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "system_cpu_count{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "cpus", "range": true, "refId": "B"}], "title": "Load", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 21}, "id": 32, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_threads_live_threads{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "live", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_threads_daemon_threads{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "daemon", "metric": "", "range": true, "refId": "B", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_threads_peak_threads{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "peak", "range": true, "refId": "C", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_threads{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "process", "range": true, "refId": "D", "step": 2400}], "title": "Threads", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "blocked"}, "properties": [{"id": "color", "value": {"fixedColor": "#bf1b00", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "new"}, "properties": [{"id": "color", "value": {"fixedColor": "#fce2de", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "runnable"}, "properties": [{"id": "color", "value": {"fixedColor": "#7eb26d", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "terminated"}, "properties": [{"id": "color", "value": {"fixedColor": "#511749", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timed-waiting"}, "properties": [{"id": "color", "value": {"fixedColor": "#c15c17", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "waiting"}, "properties": [{"id": "color", "value": {"fixedColor": "#eab839", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 21}, "id": 124, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.5.5", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_threads_states_threads{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{state}}", "range": true, "refId": "A"}], "title": "Thread States", "type": "timeseries"}, {"aliasColors": {"debug": "#1F78C1", "error": "#BF1B00", "info": "#508642", "trace": "#6ED0E0", "warn": "#EAB839"}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 18, "x": 0, "y": 28}, "height": "", "hiddenSeries": false, "id": 91, "legend": {"alignAsTable": false, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "error", "yaxis": 1}, {"alias": "warn", "yaxis": 1}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "increase(logback_events_total{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{level}}", "metric": "", "range": true, "refId": "A", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "Log Events", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "opm", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 28}, "hiddenSeries": false, "id": 61, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_files_open_files{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "open", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "process_files_max_files{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "max", "metric": "", "range": true, "refId": "B", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "File Descriptors", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 10, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "id": 129, "panels": [], "repeat": "persistence_counts", "targets": [{"datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "JVM Memory Pools (Heap)", "type": "row"}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 36}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "jvm_memory_pool_heap", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "range": true, "refId": "A", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_memory_committed_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "commited", "metric": "", "range": true, "refId": "B", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_memory_max_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max", "metric": "", "range": true, "refId": "C", "step": 1800}], "thresholds": [], "timeRegions": [], "title": "$jvm_memory_pool_heap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 43}, "id": 130, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "JVM Memory Pools (Non-Heap)", "type": "row"}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 44}, "hiddenSeries": false, "id": 78, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "jvm_memory_pool_nonheap", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "range": true, "refId": "A", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_memory_committed_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "commited", "metric": "", "range": true, "refId": "B", "step": 1800}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_memory_max_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max", "metric": "", "range": true, "refId": "C", "step": 1800}], "thresholds": [], "timeRegions": [], "title": "$jvm_memory_pool_nonheap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 58}, "id": 131, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "Garbage Collection", "type": "row"}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 59}, "hiddenSeries": false, "id": 98, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "rate(jvm_gc_pause_seconds_count{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}[1m])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{action}} ({{cause}})", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Collections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 59}, "hiddenSeries": false, "id": 101, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "rate(jvm_gc_pause_seconds_sum{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}[1m])/rate(jvm_gc_pause_seconds_count{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}[1m])", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "avg {{action}} ({{cause}})", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_gc_pause_seconds_max{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "max {{action}} ({{cause}})", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Pause Du<PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 59}, "hiddenSeries": false, "id": 99, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "rate(jvm_gc_memory_allocated_bytes_total{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "allocated", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "rate(jvm_gc_memory_promoted_bytes_total{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "promoted", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Allocated/Promoted", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "timeseries", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "Bps", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 66}, "id": 132, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "Classloading", "type": "row"}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 67}, "hiddenSeries": false, "id": 37, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_classes_loaded_classes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "loaded", "metric": "", "range": true, "refId": "A", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "Classes loaded", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 67}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "delta(jvm_classes_loaded_classes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\"}[1m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "delta-1m", "metric": "", "range": true, "refId": "A", "step": 1200}], "thresholds": [], "timeRegions": [], "title": "Class delta", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["ops", "short"], "yaxes": [{"format": "short", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 74}, "id": 133, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "-- <PERSON><PERSON> --"}, "refId": "A"}], "title": "Buffer Pools", "type": "row"}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 75}, "hiddenSeries": false, "id": 33, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_buffer_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_buffer_total_capacity_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "capacity", "metric": "", "range": true, "refId": "B", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Direct Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 75}, "hiddenSeries": false, "id": 83, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_buffer_count_buffers{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "count", "metric": "", "range": true, "refId": "A", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Direct Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 75}, "hiddenSeries": false, "id": 85, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_buffer_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "range": true, "refId": "A", "step": 2400}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_buffer_total_capacity_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "capacity", "metric": "", "range": true, "refId": "B", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Mapped Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "bytes", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "autoMigrateFrom": "graph", "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "prometheus"}, "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "rightLogBase": 1}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 75}, "hiddenSeries": false, "id": 84, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.5", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "jvm_buffer_count_buffers{namespace=\"$namespace\", wyden_service=\"$wyden_service\", pod=\"$pod\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "count", "metric": "", "range": true, "refId": "A", "step": 2400}], "thresholds": [], "timeRegions": [], "title": "Mapped Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "timeseries", "x-axis": true, "xaxis": {"mode": "time", "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "logBase": 1, "min": 0, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "30s", "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "algotrader-dev", "value": "algotrader-dev"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(namespace)", "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "access-gateway", "value": "access-gateway"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(process_uptime_seconds{namespace=\"$namespace\"}, wyden_service)", "hide": 0, "includeAll": false, "label": "Service", "multi": false, "name": "wyden_service", "options": [], "query": {"query": "label_values(process_uptime_seconds{namespace=\"$namespace\"}, wyden_service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "NC1Fiu", "value": "NC1Fiu"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(process_uptime_seconds{namespace=\"$namespace\", wyden_service=\"$wyden_service\"},pod)", "hide": 0, "includeAll": false, "label": "Pod", "multi": false, "name": "pod", "options": [], "query": {"query": "label_values(process_uptime_seconds{namespace=\"$namespace\", wyden_service=\"$wyden_service\"},pod)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allFormat": "glob", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "", "hide": 0, "includeAll": true, "label": "JVM Memory Pools Heap", "multi": false, "multiFormat": "glob", "name": "jvm_memory_pool_heap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", area=\"heap\"},id)", "refId": "Prometheus-jvm_memory_pool_heap-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "", "hide": 0, "includeAll": true, "label": "JVM Memory Pools Non-Heap", "multi": false, "multiFormat": "glob", "name": "jvm_memory_pool_nonheap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{namespace=\"$namespace\", wyden_service=\"$wyden_service\", area=\"nonheap\"},id)", "refId": "Prometheus-jvm_memory_pool_nonheap-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"now": true, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "JVM Micrometer", "uid": "bCkb9ggGz", "version": 19, "weekStart": ""}