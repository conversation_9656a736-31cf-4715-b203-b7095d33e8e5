#!/bin/bash

set -e

cd ../

echo "Creating directories and cloning all repositories"
mkdir -p oems/integration-tests
mkdir -p ui

clone_repo() {
    repo_url=$1
    repo_name=$(basename -s .git "$repo_url")

    if [ -d "$repo_name" ]; then
        echo "Repository $repo_name already cloned, skipping."
    else
        git clone "$repo_url"
    fi
}

clone_repo ssh://*******************/atcloud/access-gateway.git
clone_repo ssh://*******************/atcloud/**********************.git
clone_repo ssh://*******************/atcloud/architecture.git
clone_repo ssh://*******************/atcloud/audit-server.git
clone_repo ssh://*******************/atcloud/booking-engine.git
clone_repo ssh://*******************/atcloud/broker-config-service.git
clone_repo ssh://*******************/atcloud/clob-gateway.git
clone_repo ssh://*******************/atcloud/cloud-utils.git
clone_repo ssh://*******************/atcloud/connector-wrapper.git
clone_repo ssh://*******************/atcloud/dependency-catalog.git
clone_repo ssh://*******************/atcloud/exchange-simulator.git
clone_repo ssh://*******************/atcloud/market-data-manager.git
clone_repo ssh://*******************/atcloud/message-scheduler.git
clone_repo ssh://*******************/atcloud/order-history.git
clone_repo ssh://*******************/atcloud/pricing-service.git
clone_repo ssh://*******************/atcloud/quoting-engine.git
clone_repo ssh://*******************/atcloud/rate-service.git
clone_repo ssh://*******************/atcloud/reference-data.git
clone_repo ssh://*******************/atcloud/rest-management.git
clone_repo ssh://*******************/atcloud/schema-graphql.git
clone_repo ssh://*******************/atcloud/smart-order-router.git
clone_repo ssh://*******************/atcloud/smart-recommendation-engine.git
clone_repo ssh://*******************/atcloud/storage.git
clone_repo ssh://*******************/atcloud/target-registry.git
clone_repo ssh://*******************/atcloud/AeronMatchingEngine.git
clone_repo ssh://*******************/atcloud/matching-engine.git
clone_repo ssh://*******************/atcloud/matching-engine-collections.git
clone_repo ssh://*******************/atcloud/websocket-server.git
clone_repo ssh://*******************/atcloud/auto-hedger.git
clone_repo ssh://*******************/atcloud/quoting-order-service.git
clone_repo ssh://*******************/atcloud/processes.git
clone_repo ssh://*******************/atcloud/settlement-service.git
clone_repo ssh://*******************/atcloud/booking-wal.git
clone_repo ssh://*******************/atcloud/booking-snapshotter.git

cd ui
clone_repo ssh://*******************/atcloud/ui/ui.git
cd ..

cd oems
clone_repo ssh://*******************/atcloud/oems/execution-engine.git
clone_repo ssh://*******************/atcloud/oems/fix-api-server.git
clone_repo ssh://*******************/atcloud/oems/order-collider.git
clone_repo ssh://*******************/atcloud/oems/order-gateway.git
clone_repo ssh://*******************/atcloud/oems/rest-api-server.git
clone_repo ssh://*******************/atcloud/oems/risk-engine.git

cd integration-tests
clone_repo ssh://*******************/atcloud/oems/integration-tests/fix-actor.git
clone_repo ssh://*******************/atcloud/oems/integration-tests/load-generator.git
clone_repo ssh://*******************/atcloud/oems/integration-tests/scenario-runner.git
clone_repo ssh://*******************/atcloud/oems/integration-tests/license-server-mock.git
