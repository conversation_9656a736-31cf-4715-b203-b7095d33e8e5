#!/bin/bash

set -e

usage() {
  echo "Usage: $0 [-b] [-s] [-l] [-p]"
  echo
  echo "Rebuild all Wyden Infinity services and apply them to running docker compose setup."
  echo
  echo "Options:"
  echo "  -h    Display help"
  echo "  -b    Only build, do not restart docker compose service"
  echo "  -s    Only build services, do not rebuild libraries"
  echo "  -l    Only build libraries, do not rebuild services"
  echo "  -p    Build services in parallel"
}

starthere_path=$PWD
REBUILD_SERVICE_OPTS="--"
REBUILD_LIBRARIES=true
REBUILD_SERVICES=true
PARALLEL=false
NUM_CORES=$(getconf _NPROCESSORS_ONLN)
PARALLEL_PROCESSES=${PARALLEL_PROCESSES:-$NUM_CORES}
while getopts "hbslp" o; do
  case "${o}" in
    h)
      usage
      exit 0;
      ;;
    s)
      REBUILD_LIBRARIES=false
      ;;
    l)
      REBUILD_SERVICES=false
      ;;
    b)
      REBUILD_SERVICE_OPTS="-b"
      ;;
    p)
      PARALLEL=true
      ;;
    :)
      echo "Error: -${OPTARG} requires and argument."
      usage
      exit 255
      ;;
    *)
      usage
      exit 255
      ;;
  esac
done
shift $((OPTIND-1))

chmod +x rebuild_service.sh

LIBRARIES=(
  "dependency-catalog"
  "published-language"
  "sbe"
  "cloud-utils"
  "access-gateway-domain"
  "access-gateway-client"
  "reference-data-domain"
  "reference-data-client"
  "booking-engine-domain"
  "order-gateway-domain"
  "order-collider-domain"
  "rest-api-domain"
  "rest-api-client"
  "agency-trading-service-domain"
  "risk-engine-domain"
  "scenario-runner"
  "audit-client"
  "rate-service-domain"
  "rate-service-client"
  "pricing-service-domain"
  "pricing-service-client"
  "broker-config-domain"
  "clob-gateway-domain"
  "fix-api-domain"
  "rest-management-domain"
  "matching-engine"
  "target-registry-domain"
  "settlement-domain"
  "settlement-client"
  "auto-hedger-domain"
)

if [ "$REBUILD_LIBRARIES" = true ]; then
  for LIB in "${LIBRARIES[@]}"; do
    echo "Rebuilding ${LIB} and publishing to local maven repo"
    ./rebuild_service.sh "${REBUILD_SERVICE_OPTS}" "${LIB}"
  done
fi

SERVICES=(
  "storage"
  "access-gateway-server"
  "reference-data"
  "order-collider"
  "order-gateway"
  "market-data-manager"
  "exchange-simulator"
  "connector-wrapper-mock"
  "connector-wrapper-kraken"
  "connector-wrapper-bitmex"
  "connector-wrapper-binance-spot"
  "connector-wrapper-generic-outbound"
  "connector-wrapper-bit2me"
  "connector-wrapper-coinapi"
  "connector-wrapper-360t"
  "connector-wrapper-b2c2"
  "connector-wrapper-wintermute"
  "connector-wrapper-scrypt"
  "connector-wrapper-fireblocks"
  "ui"
  "booking-engine"
  "booking-snapshotter"
  "booking-wal"
  "booking-pnl"
  "smart-recommendation-engine"
  "smart-order-router"
  "risk-engine"
  "agency-trading-service"
  "pricing-service"
  "broker-config-service"
  "audit-server"
  "target-registry"
  "settlement"
  "rate-service"
  "order-history"
  "fix-api-trading"
  "fix-api-market-data"
  "fix-api-drop-copy"
  "fix-api-custom-ohlc"
  "rest-api-server"
  "fix-actor"
  "load-generator"
  "message-scheduler"
  "rest-management"
  "clob-gateway"
  "quoting-engine"
  "websocket-server"
  "aeron-cluster"
  "auto-hedger"
  "license-server-mock"
  "quoting-order-service"
  )

if [ "$REBUILD_SERVICES" = true ]; then
  if [ "$PARALLEL" = false ]; then
      echo "Rebuilding ${SERVICES[*]}"
    ./rebuild_service.sh ${REBUILD_SERVICE_OPTS} ${SERVICES[@]}
  fi

  if [ "$PARALLEL" = true ]; then
    echo "Rebuilding in parallel using $PARALLEL_PROCESSES processes"
    commands=()
    for SRV in "${SERVICES[@]}"; do
      commands+=("./rebuild_service.sh ${REBUILD_SERVICE_OPTS} ${SRV}")
    done
    printf "%s\n"  "${commands[@]}" | xargs -I {} -P $PARALLEL_PROCESSES sh -c "{}"
  fi
fi
