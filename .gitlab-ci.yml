image: maven:3.8.4-eclipse-temurin-17

variables:
  RELOAD_ENVIRONMENT:
    value: "false"
    options:
      - "true"
      - "false"
    description: "Reload environment: true/false"

stages:
  - lint
  - check
  - build
  - reload_db
  - reload_app
  - deploy

.reload_database: &reload_database |
  PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U $POSTGRES_USER -W -d postgres -c "drop database IF EXISTS booking_dev WITH (FORCE);"
  PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U $POSTGRES_USER -W -d postgres -c "drop database IF EXISTS reference_data WITH (FORCE);"
  PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U $POSTGRES_USER -W -d postgres -c "create database booking_dev;"
  PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U $POSTGRES_USER -W -d postgres -c "create database reference_data;"

lint:
  stage: lint
  interruptible: true
  image:
    name: bufbuild/buf:1.30.1
    entrypoint: [""]
  allow_failure: true
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - published-language/oems/**/*
      when: always
    - when: never
  script:
    - buf lint published-language/oems/src/main/proto
  tags:
    - kubernetes-aws

version-check-public-lang:
  stage: check
  interruptible: true
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - published-language/oems/version.properties
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - published-language/oems/src/main/**/*
      when: always
    - when: never
  script:
    - echo "Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes-aws

version-check-sbe:
  stage: check
  interruptible: true
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - published-language/sbe/version.properties
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - published-language/sbe/src/main/**/*
      when: always
    - when: never
  script:
    - echo "Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes-aws

backward compatibility:
  stage: check
  interruptible: true
  image:
    name: bufbuild/buf:1.30.1
    entrypoint: [""]
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - published-language/oems/**/*
      when: always
    - when: never
  script:
    - mkdir new && cp -a published-language/oems/src/main/proto/. new/
    - git fetch origin
    - git checkout origin/main
    - buf breaking new --against published-language/oems/src/main/proto
  tags:
    - kubernetes-aws

build_job:
  stage: build
  interruptible: true
  script:
    - cd published-language/oems
    - ./gradlew build
  cache: &artifact_cache
    key: "$CI_COMMIT_SHA"
    paths:
      - published-language/oems/build/
    policy: push
  rules:
    - changes:
        - published-language/oems/**/*
      when: always
    - when: never
  tags:
    - kubernetes-aws

publish_job:
  stage: deploy
  interruptible: true
  script:
    - cd published-language/oems
    - ./gradlew publish
  dependencies:
    - build_job
  cache:
    <<: *artifact_cache
    policy: pull
  rules:
    - if: "$CI_COMMIT_BRANCH == 'main'"
      changes:
        - published-language/oems/**/*
  tags:
    - kubernetes-aws

build-sbe:
  stage: build
  interruptible: true
  script:
    - cd published-language/sbe
    - ./gradlew build
  cache: &artifact_cache
    key: "$CI_COMMIT_SHA"
    paths:
      - published-language/sbe/build/
    policy: push
  rules:
    - changes:
        - published-language/sbe/**/*
      when: always
    - when: never
  tags:
    - kubernetes-aws

publish-sbe:
  stage: deploy
  interruptible: true
  script:
    - cd published-language/sbe
    - ./gradlew publish
  dependencies:
    - build-sbe
  cache:
    <<: *artifact_cache
    policy: pull
  rules:
    - if: "$CI_COMMIT_BRANCH == 'main'"
      changes:
        - published-language/sbe/**/*
  tags:
    - kubernetes-aws

reload_dev_db:
  stage: reload_db
  image: postgres
  script:
    - *reload_database
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "web" && $RELOAD_ENVIRONMENT == "true"
  tags:
    - kubernetes-aws

reload_dev_apps:
  stage: reload_app
  image: $HELM_KUBECTL_IMAGE
  script:
    - kubectl delete pods --selector app.kubernetes.io/part-of=wyden-infinity --namespace wyden-dev
    # Make sure we wait until all PODs are ready
    - kubectl wait pod --all --for=condition=Ready --selector app.kubernetes.io/part-of=wyden-infinity --namespace wyden-dev --timeout=5m
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "web" && $RELOAD_ENVIRONMENT == "true"
  tags:
    - kubernetes-aws

deploy_rabbit_dev:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade -i  --create-namespace rabbitmq bitnami/rabbitmq -n wyden-dev --timeout 10m --version 12.15.0 -f infra/rabbitmq/values-dev-aws.yaml --set-file extraSecrets.load-definition."load_definition\.json"=infra/rabbitmq/rabbit.definitions.json
  rules:
    - changes:
        - infra/**
      when: always
      if: '$CI_COMMIT_BRANCH == "main"'
  tags:
    - kubernetes-aws

## Because RabbitMQ is a dependency for the Wyden Cloud application
## and it should always be in-sync with the application version
## it can not be deployed separately, hense moved to the QA project at the moment
## because QA team decideds when the application have to be upgraded
## There is a plan to re-organise piplines later
# deploy_rabbit_uat:
#   stage: deploy
#   image: $HELM_KUBECTL_IMAGE
#   script:
#     - helm repo add bitnami https://charts.bitnami.com/bitnami
#     - helm repo update
#     - helm upgrade -i  --create-namespace rabbitmq bitnami/rabbitmq -n wyden-uat --timeout 10m --version 12.15.0 -f infra/rabbitmq/values-uat.yaml --set-file extraSecrets.load-definition."load_definition\.json"=infra/rabbitmq/rabbit.definitions.json
#   only:
#     - main
#   when: manual
#   tags:
#     - kubernetes-aws

deploy_keycloak_dev:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade --install --create-namespace keycloak bitnami/keycloak --namespace wyden-dev --timeout 10m --atomic --version $KEYCLOAK_CHART_VERSION -f infra/keycloak/values-cloud.yaml -f infra/keycloak/values-dev-aws.yaml
  rules:
    - changes:
        - infra/**
      when: always
      if: '$CI_COMMIT_BRANCH == "main"'
  tags:
    - kubernetes-aws

deploy_keycloak_uat:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade --install --create-namespace keycloak bitnami/keycloak --namespace wyden-uat --timeout 10m --atomic --version $KEYCLOAK_CHART_VERSION -f infra/keycloak/values-cloud.yaml -f infra/keycloak/values-uat-aws.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_keycloak_garanti_uat:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade --install --create-namespace keycloak bitnami/keycloak --namespace garanti-uat --timeout 10m --atomic --version $KEYCLOAK_CHART_VERSION -f infra/keycloak/values-cloud.yaml -f infra/keycloak/values-garanti-uat.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_keycloak_garanti_uat_aws:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade --install --create-namespace keycloak bitnami/keycloak --namespace garanti-uat --timeout 10m --atomic --version $KEYCLOAK_CHART_VERSION -f infra/keycloak/values-cloud.yaml -f infra/keycloak/values-garanti-uat-aws.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_keycloak_demo:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade --install --create-namespace keycloak bitnami/keycloak --namespace wyden-demo --timeout 10m --atomic --version $KEYCLOAK_CHART_VERSION -f infra/keycloak/values-cloud.yaml -f infra/keycloak/values-demo-aws.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_keycloak_cfi_sandbox:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade --install --create-namespace keycloak bitnami/keycloak --namespace cfi-sandbox --timeout 10m --atomic --version $KEYCLOAK_CHART_VERSION -f infra/keycloak/values-cloud.yaml -f infra/keycloak/values-cfi-sandbox.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_keycloak_trial:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade --install --create-namespace keycloak bitnami/keycloak --namespace wyden-trial --timeout 10m --atomic --version $KEYCLOAK_CHART_VERSION -f infra/keycloak/values-cloud.yaml -f infra/keycloak/values-trial.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_vault_dev:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add hashicorp https://helm.releases.hashicorp.com
    - helm repo update
    - helm upgrade vault-dev hashicorp/vault --install --namespace wyden-dev --create-namespace --version 0.25.0 -f infra/vault/vault/values-dev-aws.yaml
  rules:
    - changes:
        - infra/**
      when: always
      if: '$CI_COMMIT_BRANCH == "main"'
  tags:
    - kubernetes-aws

deploy_vault_garanti_uat:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add hashicorp https://helm.releases.hashicorp.com
    - helm repo update
    - helm upgrade vault-garanti-uat hashicorp/vault --install --namespace garanti-uat --create-namespace --version 0.25.0 -f infra/vault/vault/values-garanti-uat.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_vault_garanti_uat_aws:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add hashicorp https://helm.releases.hashicorp.com
    - helm repo update
    - helm upgrade vault-garanti-uat hashicorp/vault --install --namespace garanti-uat --create-namespace --version 0.25.0 -f infra/vault/vault/values-garanti-uat-aws.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_vault_uat:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add hashicorp https://helm.releases.hashicorp.com
    - helm repo update
    - helm upgrade vault-uat hashicorp/vault --install --namespace wyden-uat --create-namespace --version 0.25.0 -f infra/vault/vault/values-uat-aws.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_vault_demo:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add hashicorp https://helm.releases.hashicorp.com
    - helm repo update
    - helm upgrade vault-demo hashicorp/vault --install --namespace wyden-demo --create-namespace --version 0.25.0 -f infra/vault/vault/values-demo-aws.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_vault_cfi_sandbox:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add hashicorp https://helm.releases.hashicorp.com
    - helm repo update
    - helm upgrade vault-cfi-sandbox hashicorp/vault --install --namespace cfi-sandbox --create-namespace --version 0.25.0 -f infra/vault/vault/values-cfi-sandbox.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

deploy_vault_trial:
  stage: deploy
  image: $HELM_KUBECTL_IMAGE
  script:
    - helm repo add hashicorp https://helm.releases.hashicorp.com
    - helm repo update
    - helm upgrade vault-trial hashicorp/vault --install --namespace wyden-trial --create-namespace --version 0.25.0 -f infra/vault/vault/values-trial.yaml
  only:
    - main
  when: manual
  tags:
    - kubernetes-aws

