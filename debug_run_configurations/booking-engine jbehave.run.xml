<component name="ProjectRunConfigurationManager">
    <configuration default="false" name="booking-engine jbehave" type="JUnit" factoryName="JUnit">
        <module name="booking-engine.booking-engine.test"/>
        <extension name="coverage">
            <pattern>
                <option name="PATTERN" value="io.wyden.booking.jbehave.*"/>
                <option name="ENABLED" value="true"/>
            </pattern>
        </extension>
        <option name="PACKAGE_NAME" value="io.wyden.booking.jbehave"/>
        <option name="MAIN_CLASS_NAME" value=""/>
        <option name="METHOD_NAME" value=""/>
        <option name="TEST_OBJECT" value="package"/>
        <method v="2">
            <option name="Make" enabled="true"/>
        </method>
    </configuration>
</component>