<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Build client libraries only" type="GradleRunConfiguration" factoryName="Gradle">
    <ExternalSystemSettings>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$/dependency-catalog" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value="generateCatalogAsToml" />
          <option value="publishToMavenLocal" />
        </list>
      </option>
      <option name="vmOptions" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2">
      <option name="Gradle.BeforeRunTask" enabled="true" tasks="generateCatalogAsToml publishToMavenLocal" externalProjectPath="$PROJECT_DIR$/dependency-catalog" vmOptions="" scriptParameters="" />
      <option name="Gradle.BeforeRunTask" enabled="true" tasks="clean assemble publishToMavenLocal" externalProjectPath="$PROJECT_DIR$/architecture/published-language/oems" vmOptions="" scriptParameters="" />
      <option name="Gradle.BeforeRunTask" enabled="true" tasks="clean assemble publishToMavenLocal" externalProjectPath="$PROJECT_DIR$/cloud-utils" vmOptions="" scriptParameters="" />
      <option name="Gradle.BeforeRunTask" enabled="false" tasks="clean assemble publishToMavenLocal" externalProjectPath="$PROJECT_DIR$/reference-data" vmOptions="" scriptParameters="" />
      <option name="Gradle.BeforeRunTask" enabled="true" tasks="clean assemble publishToMavenLocal" externalProjectPath="$PROJECT_DIR$/rate-service" vmOptions="" scriptParameters="" />
      <option name="Gradle.BeforeRunTask" enabled="false" tasks="clean assemble publishToMavenLocal" externalProjectPath="$PROJECT_DIR$/market-data-manager" vmOptions="" scriptParameters="" />
      <option name="Gradle.BeforeRunTask" enabled="true" tasks="clean assemble publishToMavenLocal" externalProjectPath="$PROJECT_DIR$/oems/rest-api-server" vmOptions="" scriptParameters="" />
      <option name="Gradle.BeforeRunTask" enabled="false" tasks="clean assemble publishToMavenLocal" externalProjectPath="$PROJECT_DIR$/access-gateway/access-gateway-client" vmOptions="" scriptParameters="" />
    </method>
  </configuration>
</component>