plugins {
    id 'idea'
    id 'base'
    id 'maven-publish'
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

group 'io.wyden.config'
version '1.0.0-SNAPSHOT'

def initScript = file('../../infra/keycloak/algotrader-dev/init.sh')

artifacts {
    archives initScript
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            repositories {
                maven {
                    name 'nexus-snapshots'
                    url 'https://repo.wyden.io/nexus/repository/snapshots/'
                    credentials {
                        username repository_username
                        password repository_password
                    }
                }
            }
            artifact initScript
        }
    }
}

repositories {
    mavenCentral()
}