#!/bin/bash

usage() { echo "Usage: $0 [-h] NAMESPACE" 1>&2; exit 1; }

while getopts "h" o; do
  case "${o}" in
    h)
      usage
      ;;
    *)
      usage
      ;;
  esac
done
shift $((OPTIND-1))

if [ -z "$1" ]; then usage; fi

NAMESPACE="${1}"

case $NAMESPACE in
    wyden-dev)
        PREFIX=""
        SUFFIX="-wyden-fix-actor"
        CONNECTOR_MOCK="connector-wrapper-mock"
        ;;
    default)
        PREFIX="wyden"
        SUFFIX=""
        ;;
    wyden-qa)
        PREFIX="wyden-qa-"
        SUFFIX="-qa-wyden-fix-actor"
        CONNECTOR_MOCK="wyden-mock"
        ;;
    wyden-uat)
        PREFIX="wyden-uat-"
        SUFFIX=""
        CONNECTOR_MOCK="wyden-mock"
        ;;
    wyden-runner)
        PREFIX="wyden-"
        SUFFIX="-wyden-fix-actor"
        CONNECTOR_MOCK="wyden-mock"
        ;;
    *)
        PREFIX=""
        SUFFIX=""
        ;;
esac

kubectl port-forward -n "$NAMESPACE" statefulset/${PREFIX}storage 8070:8070 &
kubectl port-forward -n "$NAMESPACE" statefulset/keycloak 8080:8080 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}access-gateway 8089:8089 &
kubectl port-forward -n "$NAMESPACE" deployment/fix-actor${SUFFIX} 8090:8090 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}order-collider 8091:8091 &
kubectl port-forward -n "$NAMESPACE" deployment/${CONNECTOR_MOCK} 8092:8092 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}fix-api 8093:8093 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}order-gateway 8094:8094 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}rest-api 8095:8095 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}rate-service 8052:8052 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}market-data-manager 8096:8096 &
#kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}execution-engine 8097:8097 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}rest-management 8083:8083 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}reference-data 8098:8098 &
#kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}connector-wrapper-bitmex 8099:8099 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}booking-engine 8100:8100 &
#kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}connector-wrapper-binance-spot 8101:8101 &
#kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}connector-wrapper-generic-outbound 8102:8102 &
#kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}exchange-simulator 10000:10000 10001:10001 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}smart-recommendation-engine 8104:8104 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}smart-order-router 8060:8060 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}risk-engine 8300:8300 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}audit-server 8030:8030 &
#kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}order-history 8040:8040 &
kubectl port-forward -n "$NAMESPACE" deployment/${PREFIX}settlement-server 8067:8067 &
