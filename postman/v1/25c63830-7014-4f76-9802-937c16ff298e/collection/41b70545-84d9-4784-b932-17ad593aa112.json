{"info": {"_postman_id": "41b70545-84d9-4784-b932-17ad593aa112", "name": "Rest API Server", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Refresh securities", "id": "6e5f1345-fd84-4baa-b5b5-969b65960254", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8095/api/securities/refresh", "protocol": "http", "host": ["localhost"], "port": "8095", "path": ["api", "securities", "refresh"]}}, "response": []}, {"name": "Create client-side security", "id": "17655543-808f-4712-8581-b65fe5e23dc4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation CreateClientSideSecurity($request: CreateClientSideSecurityInput!) {\r\n\tcreateClientSideSecurity(request: $request) {\r\n        status\r\n  }  \r\n}", "variables": "{\r\n  \"request\": {\r\n                \"baseSecurity\": {\r\n                    \"assetClass\": \"FOREX\",\r\n                    \"description\": null,\r\n                    \"feeCurrency\": null,\r\n                    \"inverseContract\": false,\r\n                    \"quoteCurrency\": \"USDT\",\r\n                    \"settlementCurrency\": null,\r\n                    \"venueName\": \"WydenMock\"\r\n                },\r\n                \"forexSpotProperties\": {\r\n                    \"baseCurrency\": \"MATIC\"\r\n                },\r\n                \"tradingConstraints\": {\r\n                    \"contractSize\": null,\r\n                    \"maxPrice\": null,\r\n                    \"maxQty\": \"1\",\r\n                    \"minNotional\": null,\r\n                    \"minPrice\": null,\r\n                    \"minQty\": \"1\",\r\n                    \"priceIncr\": null,\r\n                    \"priceScale\": null,\r\n                    \"qtyIncr\": null,\r\n                    \"qtyScale\": null,\r\n                    \"tradeable\": true\r\n                },\r\n                \"securityIdentifiers\": {\r\n                }\r\n            }\r\n  }"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get venue accounts", "id": "7d8d0c34-50b0-4194-b607-ba00c89f0496", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n    venueAccounts {\r\n        venue\r\n        venueAccounts\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get venue names (CSS)", "id": "746bbb6d-58b3-4d2c-a23b-2e9d9823d375", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "{\r\n    clientSideSecuritiesVenueNames\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update client-side security", "id": "e6e93238-703e-46b9-8894-29a3a41c6091", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateSecurity($request: UpdateSecurityInput) {\r\n\tupdateSecurity(request: $request) {\r\n        status\r\n  }  \r\n}", "variables": "{\r\n  \"request\": {\r\n                \"baseSecurity\": {\r\n                    \"description\": \"E4FF\",\r\n                    \"feeCurrency\": \"8EA4\",\r\n                    \"inverseContract\": false,\r\n                    \"settlementCurrency\": \"A978\",\r\n                    \"displayName\": \"0B05\"\r\n                },\r\n                \"tradingConstraints\": {\r\n                    \"contractSize\": \"713\",\r\n                    \"maxPrice\": \"357\",\r\n                    \"maxQty\": \"70\",\r\n                    \"minNotional\": \"840\",\r\n                    \"minPrice\": \"405\",\r\n                    \"minQty\": \"835\",\r\n                    \"priceIncr\": \"374\",\r\n                    \"priceScale\": \"643\",\r\n                    \"qtyIncr\": \"829\",\r\n                    \"qtyScale\": \"401\"\r\n                },\r\n                \"archived\": false,\r\n                \"symbol\": \"7FFD75E5@FOREX@WydenMock\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update street-side security", "id": "7bb8bafa-8ea9-4341-b71e-08f205c7945c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateSecurity($request: UpdateSecurityInput) {\r\n\tupdateSecurity(request: $request) {\r\n        status\r\n  }  \r\n}", "variables": "{\r\n  \"request\": {\r\n    \"baseSecurity\": {\r\n        \"displayName\": \"elo\"\r\n    },\r\n    \"symbol\": \"BTCUSDT@FOREX@WydenMock\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create venue account", "id": "3c9e3b82-0b82-4a9c-b0df-3326094e366c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CreateVenueAccount($request: CreateVenueAccountInput!) {\r\n\tcreateVenueAccount(request: $request) {\r\n        status\r\n    }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"venueAccountName\": \"First_Sirian_BANK\",\r\n        \"venueName\": \"tomek_test_venue\"\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get user data", "id": "69c68129-c904-4c61-b9d5-a5f9be4589e4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJHeXI0cjU0UVFoTW01WmpyUm9CaDMza3UtZjg3Vm54d21jRFFDZmF3MXhRIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PsPu79V-iVt5CFUEtFnUrGRZ6s_j8mMOEjuYvtghQHAFszPmbv4c5d_yvRneo18rfafgKwtYEFHl75jtVyLqurSXJ3BmdsREf4DRJXzHRkreov7TH9Cqf6srjovlIsIzfKE56jh8iYrHS-q87w-95NTlRLDr9ipnYhYw2fyh7WFEDgKCeqnTreYrzCnO3BQVXfg_4DyXrfzAumzqkb13W7zJkL9BJmvB_qjpHeBx7w0yxk2QQOryMNhpkNXBMktIV2yMhxIgXU34eI_70NpucJ9YxZkWSnTRFgmlL_3MSQCcK1s7xOSnbPkyTHOupTw6iZIciWR_4qO1fGtxeAJLFA", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query UserData {\r\n    userData {\r\n        data\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update user data", "id": "1758b330-547a-40db-8a75-2e32def04018", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateUserData($request: UpdateUserDataInput!) {\r\n\tupdateUserData(request: $request) {\r\n        data\r\n    }\r\n}", "variables": "{\r\n    \"request\": {    \r\n        \"data\": \"\"\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Send order - client", "id": "ec42bd8d-3cdc-434a-908d-304717df92cf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation SendOrder($request: NewOrderSingleRequestInput!) {\r\n  sendOrder(request: $request) {\r\n    clientId\r\n    __typename\r\n  }\r\n}", "variables": "{\r\n  \"request\": {\r\n    \"clientId\": \"trader\",\r\n    \"clOrderId\": \"admin-16\",\r\n    \"symbol\": \"MATICUSDT@FOREX@tomek_test_venue\",\r\n    \"side\": \"SELL\",\r\n    \"orderType\": \"MARKET\",\r\n    \"quantity\": \"1\",\r\n    \"price\": null,\r\n    \"tif\": \"DAY\",\r\n    \"targetVenueAccount\": \"First_Sirian_BANK\",\r\n    \"portfolioId\": \"d15bd122-4a52-45ee-b306-a8fb2d86c133\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Send order - street", "id": "8fd1eef9-12b6-4274-9f2a-57748defd1cf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation SendOrder($request: NewOrderSingleRequestInput!) {\r\n  sendOrder(request: $request) {\r\n    clientId\r\n    __typename\r\n  }\r\n}", "variables": "{\r\n  \"request\": {\r\n    \"clientId\": \"trader\",\r\n    \"clOrderId\": \"admin-003\",\r\n    \"symbol\": \"MATICUSDT@FOREX@BitMEX\",\r\n    \"side\": \"SELL\",\r\n    \"orderType\": \"MARKET\",\r\n    \"quantity\": \"1\",\r\n    \"price\": null,\r\n    \"tif\": \"DAY\",\r\n    \"targetVenueAccount\": \"BITMEX-testnet1\",\r\n    \"portfolioId\": \"d15bd122-4a52-45ee-b306-a8fb2d86c133\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Healthcheck", "id": "75c98940-ce8d-4fc9-97db-8cd1cd1d775d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8095/actuator/health", "protocol": "http", "host": ["localhost"], "port": "8095", "path": ["actuator", "health"]}}, "response": []}, {"name": "Get currencies", "id": "ee3d0644-09f7-4615-9b0e-1818ca919fa2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query FindAllSecuritiesByVenueName($request: [String!]) {\r\n  securities(venueNames: $request) {\r\n    baseSecurity {\r\n        assetClass\r\n        description\r\n        feeCurrency\r\n        inverseContract\r\n        quoteCurrency\r\n        settlementCurrency\r\n        venueName\r\n        venueType\r\n        displayName\r\n    }\r\n    forexSpotProperties {\r\n        baseCurrency\r\n    }\r\n    tradingConstraints {\r\n        contractSize\r\n        maxPrice\r\n        maxQty\r\n        minNotional\r\n        minPrice\r\n        minQty\r\n        priceIncr\r\n        priceScale\r\n        qtyIncr\r\n        qtyScale\r\n        tradeable\r\n    }\r\n    securityIdentifiers {\r\n      adapterTicker\r\n      symbol\r\n    }\r\n    archivedAt\r\n  }\r\n}", "variables": "{\r\n    \"request\": []\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create portfolio", "id": "55d3d280-8ea0-47a6-a813-b0a4655585e1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CreatePortfolio($request: CreatePortfolioInput!) {\r\n    createPortfolio(request: $request) {\r\n        status\r\n    }\r\n}", "variables": "{\r\n    \"request\": {\r\n      \t\"name\": \"portfolio_trader_2\",\r\n        \"portfolioCurrency\": \"USD\",\r\n\t    \"tags\": [\r\n\t\t    {\r\n                \"key\": \"region\",\r\n                \"value\": \"EMEA\"\r\n            },\r\n\t\t    {\r\n                \"key\": \"asset_class\",\r\n                \"value\": \"FOREX\"\r\n            }\r\n        ]\r\n    }  \r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update portfolio", "id": "a0991518-7ded-46da-a993-cfc281552f1f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdatePortfolio($request: UpdatePortfolioInput!) {\r\n    updatePortfolio(request: $request) {\r\n        status\r\n    }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"id\": \"1fad5d09-3239-40c7-83a5-42595245238d\",\r\n        \"name\": \"portfolio1_archived\",\r\n        \"archived\": true\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update user permissions", "id": "fc04eeb0-e7de-4d44-9431-4e90114d1e98", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateUserPermissions($request: UpdateUserPermissionsInput!) {\r\n    updateUserPermissions(request: $request) {\r\n        username\r\n        permissions {\r\n            resource\r\n            scope\r\n            resourceId\r\n        }\r\n    }\r\n}\r\n", "variables": "{\r\n    \"request\": {\r\n        \"username\": \"admin\", \r\n        \"permissions\": [\r\n            {\r\n                \"resource\": \"venue.account\",\r\n                \"scope\": \"read\",\r\n                \"resourceId\": \"BITMEX-testnet1\"\r\n            },\r\n            {\r\n                \"resource\": \"venue.account\",\r\n                \"scope\": \"trading\",\r\n                \"resourceId\": \"BITMEX-testnet1\"\r\n            }\r\n        ]\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get portfolios", "id": "3bb95054-043d-46ca-99f8-88713069bb20", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetPortfolios {\r\n    portfolios {\r\n        id\r\n        name\r\n        createdAt\r\n        portfolioCurrency\r\n        archivedAt\r\n        tags {\r\n            key\r\n            value\r\n        }\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Current orders", "id": "b2e88c42-865b-48d2-8a66-2c7a7bee9868", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query CurrentOrders {\r\n  currentOrders {\r\n    avgPrice\r\n    clOrderId\r\n    clientId\r\n    cumQty\r\n    execType\r\n    executionId\r\n    portfolioId\r\n    lastPrice\r\n    lastQty\r\n    leavesQty\r\n    orderId\r\n    orderQty\r\n    orderStatus\r\n    orderStatusRequestId\r\n    origClOrderId\r\n    reason\r\n    securityType\r\n    side\r\n    symbol\r\n    targetVenueAccount\r\n    targetVenueTicker\r\n    targetVenueTimestamp\r\n    text\r\n    timestamp\r\n    __typename\r\n  }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get positions (VA)", "id": "44f24aec-50a3-4fc9-a599-6ceaf1a133ea", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query Positions($search: AccountingSearchInput!) {\r\n  positions(search: $search) {\r\n        edges {\r\n            cursor\r\n            node {\r\n                updatedAt\r\n                quantity\r\n                bookingCurrency\r\n                netRealizedPnl\r\n                grossRealizedPnl\r\n                netCost\r\n                grossCost\r\n                reference\r\n                instrument\r\n                currency\r\n                security\r\n                portfolio\r\n                account\r\n                notionalQuantity\r\n                marketValue\r\n                marketValuePc\r\n                netRealizedPnlPc\r\n                netCostPc\r\n                grossRealizedPnlPc\r\n                grossCostPc\r\n                netAveragePrice\r\n                grossAveragePrice\r\n                netUnrealizedPnl\r\n                grossUnrealizedPnl\r\n                netUnrealizedPnlPc\r\n                grossUnrealizedPnlPc\r\n            }\r\n        }\r\n        pageInfo {\r\n            hasNextPage\r\n            endCursor\r\n        }\r\n  }\r\n}", "variables": "{\r\n    \"search\": {\r\n        \"venueAccounts\": [\r\n            \"BITMEX-testnet1\"\r\n\r\n        ]\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get ledger entries", "id": "0ec1d7d3-bf6f-4d4b-9b22-51e498937726", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query LedgerEntries($search: AccountingSearchInput!) {\r\n    ledgerEntries(search: $search) {\r\n        edges {\r\n            cursor\r\n            node {\r\n                updatedAt\r\n                quantity\r\n                price\r\n                fee\r\n                type\r\n                reference\r\n                instrument\r\n                currency\r\n                security\r\n                portfolio\r\n                account\r\n            }\r\n        }\r\n        pageInfo {\r\n            hasNextPage\r\n            endCursor\r\n        }\r\n    }\r\n}", "variables": "{\r\n    \"search\": {\r\n        \"portfolio\": [\r\n            \"b30f1dbe-8c14-4106-814e-e5c196f37d0a\"\r\n        ]\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get transaction", "id": "156a5bdd-8727-4d7b-8559-7c26392d32d7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query Transactions($search: TransactionSearchInput!) {\r\n    transactions(search: $search) {\r\n        edges {\r\n            node {\r\n                __typename\r\n                ... on ClientCashTrade {\r\n                    uuid\r\n                    updatedAt\r\n                    extTransactionId\r\n                    fee\r\n                    feeCurrency\r\n                    description\r\n                    quantity\r\n                    price\r\n                    currency\r\n                    intOrderId\r\n                    extOrderId\r\n                    baseCurrency\r\n                    portfolio\r\n                    counterPortfolio\r\n                }\r\n                ... on StreetAssetTrade {\r\n                    uuid\r\n                    updatedAt\r\n                    extTransactionId\r\n                    fee\r\n                    feeCurrency\r\n                    description\r\n                    quantity\r\n                    price\r\n                    currency\r\n                    intOrderId\r\n                    extOrderId\r\n                    security\r\n                    portfolio\r\n                    venueAccount\r\n                }\r\n                ... on ClientAssetTrade {\r\n                    uuid\r\n                    updatedAt\r\n                    extTransactionId\r\n                    fee\r\n                    feeCurrency\r\n                    description\r\n                    quantity\r\n                    price\r\n                    currency\r\n                    intOrderId\r\n                    extOrderId\r\n                    security\r\n                    portfolio\r\n                    counterPortfolio\r\n                }\r\n                ... on StreetCashTrade {\r\n                    uuid\r\n                    updatedAt\r\n                    extTransactionId\r\n                    fee\r\n                    feeCurrency\r\n                    description\r\n                    quantity\r\n                    price\r\n                    currency\r\n                    intOrderId\r\n                    extOrderId\r\n                    baseCurrency\r\n                    portfolio\r\n                    venueAccount\r\n                }\r\n            }\r\n            cursor\r\n        }\r\n        pageInfo {\r\n            hasNextPage\r\n            endCursor\r\n        }\r\n    }\r\n}\r\n", "variables": "{\r\n    \"search\": {\r\n        \"currency\": [\"LTC\"]\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create trading engine", "id": "********-9de0-4ef4-88ad-394297173ee4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CreateTradingEngine($request: TradingEngineConfigInput!) {\r\n    createTradingEngine(request: $request) {\r\n        clientId\r\n    }\r\n}\r\n", "variables": "{\r\n    \"request\": {\r\n      \t\"symbol\": \"LTCBNB@FOREX@Bank\",\r\n        \"lowThreshold\": -100000,\r\n      \t\"highThreshold\": 100000,\r\n        \"targetExposure\": 0,\r\n        \"markupPercentage\": 0,\r\n        \"counterPortfolioId\": \"BANK_Portfolio\",\r\n        \"streetSideReferenceInstrument\": \"LTCBNB@FOREX@Simulator\",\r\n        \"enabled\": true\r\n    }  \r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create autohedger", "id": "8fb9e80d-ccb6-404e-9c46-1c79cb645fcb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CreateTradingEngine($request: TradingEngineConfigInput!) {\r\n    createTradingEngine(request: $request) {\r\n        clientId\r\n    }\r\n}\r\n", "variables": "{\r\n    \"request\": {\r\n      \t\"symbol\": \"LTCBNB@FOREX@Bank\",\r\n        \"lowThreshold\": -100000,\r\n      \t\"highThreshold\": 100000,\r\n        \"targetExposure\": 0,\r\n        \"markupPercentage\": 0,\r\n        \"counterPortfolioId\": \"BANK_Portfolio\",\r\n        \"streetSideReferenceInstrument\": \"LTCBNB@FOREX@Simulator\",\r\n        \"enabled\": true\r\n    }  \r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get trading engine config", "id": "f84c1491-fe8b-4c54-bba9-59227d3be787", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetTradingEnginesConfig {\r\n    tradingEngineConfig {\r\n        symbol,\r\n        lowThreshold,\r\n        highThreshold,\r\n        targetExposure,\r\n        markupPercentage,\r\n        counterPortfolioId,\r\n        streetSideReferenceInstrument,\r\n        enabled\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Delete trading engine config", "id": "233ebdc5-80a4-46cc-a056-2f95cd41f00d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation DeleteCreateTradingEngine($request: TradingEngineConfigKey!) {\r\n    deleteTradingEngine(request: $request) {\r\n        clientId\r\n    }\r\n}\r\n", "variables": "{\r\n    \"request\": {\r\n      \t\"symbol\": \"BTCUSDT@FOREX@Bank\",\r\n        \"counterPortfolioId\": \"BANK_Portfolio\"\r\n    }  \r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}]}