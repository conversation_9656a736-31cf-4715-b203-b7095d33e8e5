{"info": {"_postman_id": "1c7a1801-881a-4746-90ed-b48458186f76", "name": "Booking Engine", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get positions", "id": "2118717d-9b3d-4dcc-97e8-51bde0b79813", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "C", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8100/positions?venueAccount", "protocol": "http", "host": ["localhost"], "port": "8100", "path": ["positions"], "query": [{"key": "symbol", "value": "BE4D", "disabled": true}, {"key": "portfolio", "value": "First_<PERSON><PERSON>_BANK_Portfolio", "disabled": true}, {"key": "venueAccount", "value": null}, {"key": "symbol", "value": "DF05", "disabled": true}]}}, "response": []}, {"name": "Get transactions", "id": "f3471a03-bb1d-40df-b916-25f4482f70b7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "C", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n        \"portfolio\": [\r\n            \"Retail1_simple\"\r\n        ],\r\n        \"first\": 100\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:63625/transactions/search", "protocol": "http", "host": ["localhost"], "port": "63625", "path": ["transactions", "search"], "query": [{"key": "symbol", "value": "BE4D", "disabled": true}, {"key": "portfolio", "value": "First_<PERSON><PERSON>_BANK_Portfolio", "disabled": true}, {"key": "symbol", "value": "DF05", "disabled": true}]}}, "response": []}, {"name": "Get transaction by id", "id": "e6226385-d7b7-4b2c-99d8-787dec807d22", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "C", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n        \"portfolio\": [\r\n            \"Retail1_simple\"\r\n        ],\r\n        \"first\": 100\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:63625/transactions/search", "protocol": "http", "host": ["localhost"], "port": "63625", "path": ["transactions", "search"], "query": [{"key": "symbol", "value": "BE4D", "disabled": true}, {"key": "portfolio", "value": "First_<PERSON><PERSON>_BANK_Portfolio", "disabled": true}, {"key": "symbol", "value": "DF05", "disabled": true}]}}, "response": []}, {"name": "Reset engine", "id": "09a14d4a-9ab0-493a-be6f-d6123670d925", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"symbol\": [\"test\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8100/reset", "protocol": "http", "host": ["localhost"], "port": "8100", "path": ["reset"], "query": [{"key": "symbol", "value": "AA", "disabled": true}]}}, "response": []}]}