{"id": "e00323ff-4011-4e87-9fa6-0b9f5b7027cc", "name": "<PERSON>", "values": [{"key": "gqlUrl", "value": "https://testing-dev.wyden.io/rest-api/graphql", "enabled": true, "type": "default"}, {"key": "<PERSON><PERSON>", "value": "", "enabled": true, "type": "default"}, {"key": "keycloakHost", "value": "https://keycloak-dev.wyden.io", "enabled": true, "type": "default"}, {"key": "restApiUrl", "value": "https://testing-dev.wyden.io/rest-api", "enabled": true, "type": "default"}, {"key": "rest.mgmt.url", "value": "https://wyden-dev-api-mgmt.wyden.io", "enabled": true, "type": "default"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "270bacd0-29c3-4ec4-bca1-58fa507fdb83", "enabled": true, "type": "default"}, {"key": "apiSecret", "value": "g8p7@!7k!ho5f@jw", "enabled": true, "type": "default"}, {"key": "wssUrl", "value": "wss://wyden-dev-api.wyden.io/graphql/ws", "enabled": true, "type": "default"}, {"key": "wssUrl2", "value": "wss://testing-dev.wyden.io/rest-api/graphql/ws", "enabled": true, "type": "default"}]}