{"info": {"_postman_id": "d6040071-1a7b-4233-8e39-6cd76a68baaf", "name": "Execution Engine", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Reset engine", "id": "bf9f579b-fa66-4ce2-8958-39fe228ffea8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{exec.engine.url}}/api/trading/DOGEUSD@FOREX@Bank", "host": ["{{exec.engine.url}}"], "path": ["api", "trading", "DOGEUSD@FOREX@Bank"]}}, "response": []}, {"name": "Health check", "id": "86e3566f-a31c-4855-b71b-11d72d82a4e9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{exec.engine.url}}/actuator/health", "host": ["{{exec.engine.url}}"], "path": ["actuator", "health"]}}, "response": []}, {"name": "Get engine state", "id": "f764e59e-6426-458a-b1ac-9f9641446179", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{exec.engine.url}}/api/trading/DOGEUSD@FOREX@Bank/status", "host": ["{{exec.engine.url}}"], "path": ["api", "trading", "DOGEUSD@FOREX@Bank", "status"]}}, "response": []}, {"name": "Delete trading engine", "id": "e0c8bb00-3131-4730-b2f1-b8fe829ef2f4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{exec.engine.url}}/trading-engine?symbol=BTCUSDT@FOREX@Bank&counterPortfolioId=e2e_BANK_Portfolio", "host": ["{{exec.engine.url}}"], "path": ["trading-engine"], "query": [{"key": "symbol", "value": "BTCUSDT@FOREX@Bank"}, {"key": "counterPortfolioId", "value": "e2e_BANK_Portfolio"}]}}, "response": []}, {"name": "Configure trading engine", "id": "6644d697-9fb7-4f4f-81a2-f5a65618a79c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{exec.engine.url}}/", "host": ["{{exec.engine.url}}"], "path": [""]}}, "response": []}, {"name": "Get trading engine config", "id": "34db2c29-0727-4fd8-8ff5-af22ad54e019", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{exec.engine.url}}/trading-engine", "host": ["{{exec.engine.url}}"], "path": ["trading-engine"]}}, "response": []}, {"name": "Get auto hedger config", "id": "4ca5cbd1-7d06-4421-9020-fe4085347c63", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{exec.engine.url}}/auto-hedger", "host": ["{{exec.engine.url}}"], "path": ["auto-hedger"]}}, "response": []}, {"name": "Get logger", "id": "d80780e5-a358-433f-9fb6-1bce3ae0abe4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{exec.engine.url}}/actuator/loggers/io.wyden.executionengine.service.brokerdesk.BrokerDeskQuotingService", "host": ["{{exec.engine.url}}"], "path": ["actuator", "loggers", "io.wyden.executionengine.service.brokerdesk.BrokerDeskQuotingService"]}}, "response": []}, {"name": "Update logger", "id": "3454ab07-5d11-4420-bfa2-b674c21c6d45", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"configuredLevel\": \"DEBUG\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{exec.engine.url}}/actuator/loggers/io.wyden.executionengine.service.brokerdesk.BrokerDeskQuotingService", "host": ["{{exec.engine.url}}"], "path": ["actuator", "loggers", "io.wyden.executionengine.service.brokerdesk.BrokerDeskQuotingService"]}}, "response": []}]}