{"info": {"_postman_id": "06a16b94-4299-46bc-8c5f-b6a309db2fac", "name": "Reference Data", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get all securities", "id": "134c19ae-3c54-4e75-b39d-51bf28adb483", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{ref.data.url}}/instruments", "host": ["{{ref.data.url}}"], "path": ["instruments"]}}, "response": []}, {"name": "Get portfolios", "id": "0be5ed92-14eb-4c33-ad5f-e841e27e2f19", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{ref.data.url}}/portfolios", "host": ["{{ref.data.url}}"], "path": ["portfolios"]}}, "response": []}, {"name": "Refresh street side securities", "id": "80f626aa-d74c-4d54-8067-e6e6224d5172", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"venue\": \"simulator\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ref.data.url}}/instruments/street/refresh", "host": ["{{ref.data.url}}"], "path": ["instruments", "street", "refresh"]}}, "response": []}, {"name": "Remove venue account", "id": "3807db80-f1cf-4a65-85d9-e77ec6780558", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{ref.data.url}}/venue-accounts/First_Sirian_BANK", "host": ["{{ref.data.url}}"], "path": ["venue-accounts", "First_<PERSON><PERSON>_BANK"]}}, "response": []}, {"name": "Remove e2e venue accounts", "id": "8ae1c3a0-158b-4ad9-95fc-4235190fa0ce", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{ref.data.url}}/venue-accounts/e2e", "host": ["{{ref.data.url}}"], "path": ["venue-accounts", "e2e"]}}, "response": []}, {"name": "Remove e2e securities", "id": "6a0e10ca-043b-4195-b87a-b6e5ddf7c4d3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{ref.data.url}}/securities/e2e", "host": ["{{ref.data.url}}"], "path": ["securities", "e2e"]}}, "response": []}, {"name": "Remove e2e portfolios", "id": "3562a3b3-c5b5-4eab-949a-d63ed1d14db4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{ref.data.url}}/portfolios/e2e", "host": ["{{ref.data.url}}"], "path": ["portfolios", "e2e"]}}, "response": []}, {"name": "Health check", "id": "f4bd7796-2680-442b-bc25-fe98238aa3fc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{ref.data.url}}/actuator/health/readiness", "host": ["{{ref.data.url}}"], "path": ["actuator", "health", "readiness"]}}, "response": []}, {"name": "Get venue accounts", "id": "58b55a2e-d9a4-4353-9ca6-42b676a66ace", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{ref.data.url}}/venue-accounts", "host": ["{{ref.data.url}}"], "path": ["venue-accounts"]}}, "response": []}]}