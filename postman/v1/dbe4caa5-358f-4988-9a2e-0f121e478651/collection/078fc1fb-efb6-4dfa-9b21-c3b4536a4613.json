{"info": {"_postman_id": "078fc1fb-efb6-4dfa-9b21-c3b4536a4613", "name": "Rest API Server", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "REST", "item": [{"name": "Get pretrade checks", "id": "7f599dbd-7714-4f9b-900e-3ba0386c0433", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{rest.api.url}}/api/risk/pretradechecks", "host": ["{{rest.api.url}}"], "path": ["api", "risk", "pretradechecks"]}}, "response": []}, {"name": "Delete pretradecheck", "id": "2c8f0bfc-fd1a-4839-9548-3721fa2b1121", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{rest.api.url}}/api/risk/pretradechecks/e2e_2040321756", "host": ["{{rest.api.url}}"], "path": ["api", "risk", "pretradechecks", "e2e_2040321756"]}}, "response": []}, {"name": "Save pretrade check", "id": "9965e71d-6f5c-4871-883b-7afecea7c44c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": \"12345\",\r\n    \"type\": \"Instrument\",\r\n    \"level\": \"WARN\",\r\n    \"portfolios\": [\"Trader\"],\r\n    \"portfolio_tags\": {\r\n        \"key\": \"value\",\r\n        \"key2\":\"value2\"\r\n    },\r\n    \"properties\": {\r\n        \"allowlist\": {\r\n            \"stringArray\": {\r\n                \"stringValue\": [\"ETHUSD\"]\r\n            }\r\n        }\r\n    },\r\n    \"request_channels\": [\"UI\"],\r\n    \"enabled\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.api.url}}/api/risk/pretradechecks", "host": ["{{rest.api.url}}"], "path": ["api", "risk", "pretradechecks"]}}, "response": []}], "id": "03facccc-fcfe-4757-8bd0-f466269456f2"}, {"name": "deprecated - Batch save client side security", "id": "744f28d1-636a-4682-adfc-5076159b6b8b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"test security\",\r\n    \"minQty\": \"10.015\",\r\n    \"maxQty\": \"0.05\",\r\n    \"clientId\": \"First_Sirian_BANK\",\r\n    \"baseCurrency\": \"elo\",\r\n    \"quoteCurrency\": \"elo2\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8095/securities", "protocol": "http", "host": ["localhost"], "port": "8095", "path": ["securities"]}}, "response": []}, {"name": "deprecated - Update client side security", "id": "16e7227e-04ab-43c9-bcaf-36c719bb2134", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "    {\r\n        \"baseSecurity\": {\r\n            \"venueName\": \"First_Sirian_BANK\",\r\n            \"quoteCurrency\": \"USD\"\r\n        },\r\n        \"forexSpotProperties\": {\r\n            \"baseCurrency\": \"ETH\"\r\n        },\r\n        \"tradingConstraints\": {\r\n            \"minQty\": \"10.001\",\r\n            \"maxQty\": \"15\"\r\n        }\r\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8095/securities/client", "protocol": "http", "host": ["localhost"], "port": "8095", "path": ["securities", "client"]}}, "response": []}, {"name": "Add user permissions", "id": "1b87b724-8292-43d7-97af-c15b9fea75b7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation ($request: AddOrRemoveUserPermissionsInput!) {\r\n    addUserPermissions(request: $request) {\r\n        clientId\r\n  }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"username\": \"admin\",\r\n        \"permissions\": [\r\n                {\r\n                    \"resource\": \"portfolio\",\r\n                    \"scope\": \"trade\",\r\n                    \"resourceId\": \"portfoliotrader1\"\r\n                }\r\n        ]\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Remove user permissions", "id": "a8323959-a436-44c0-a938-579669c0344e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation ($request: AddOrRemoveUserPermissionsInput!) {\r\n    removeUserPermissions(request: $request) {\r\n        clientId\r\n  }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"username\": \"admin\",\r\n        \"permissions\": [\r\n                {\r\n                    \"resource\": \"venue.account\",\r\n                    \"scope\": \"trading\",\r\n                    \"resourceId\": \"WydenMock_132\"\r\n                }\r\n        ]\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Refresh securities", "id": "eedcdbcc-dba2-426c-8765-1e050b97f96c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{rest.api.url}}/api/securities/refresh", "host": ["{{rest.api.url}}"], "path": ["api", "securities", "refresh"]}}, "response": []}, {"name": "Create client-side security", "id": "5960295a-fd00-4f0c-a182-cc936a843ea0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation ($request: CreateClientSideInstrumentInput) {\r\n\tcreateClientSideInstrument(request: $request) {\r\n        status\r\n    }\r\n  }", "variables": "{\r\n  \"request\": {\r\n    \"baseInstrument\": {\r\n      \"venueName\": \"Bank\",\r\n      \"quoteCurrency\": \"USDTQ\",\r\n      \"assetClass\": \"FOREX\",\r\n      \"inverseContract\": false\r\n    },\r\n    \"forexSpotProperties\": {\r\n      \"baseCurrency\": \"BASE\"\r\n    },\r\n    \"tradingConstraints\": {\r\n      \"minQty\": \"0\",\r\n      \"maxQty\": \"*********9\",\r\n      \"tradeable\": true\r\n    },\r\n    \"instrumentIdentifiers\": {\r\n        \"tradingViewId\": \"BASEUSDTQ\",\r\n        \"venueTradingViewId\": \"qwerty\"\r\n    }\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get all instruments", "id": "d110c9c2-4d2d-442e-ad68-fb75df5e9a5b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n  instruments(venueNames: []) {\r\n    baseInstrument {\r\n      venueName\r\n      assetClass\r\n      description\r\n      quoteCurrency\r\n      feeCurrency\r\n      settlementCurrency\r\n      inverseContract\r\n      venueType\r\n      description\r\n    }\r\n    instrumentIdentifiers {\r\n      adapterTicker\r\n      instrumentId\r\n      tradingViewId\r\n      venueTradingViewId\r\n    }\r\n    tradingConstraints {\r\n      minQty\r\n      maxQty\r\n      qtyIncr\r\n      minPrice\r\n      maxPrice\r\n      priceIncr\r\n      minNotional\r\n      priceScale\r\n      qtyScale\r\n      contractSize\r\n      tradeable\r\n    }\r\n    forexSpotProperties {\r\n      baseCurrency\r\n    },\r\n    archivedAt\r\n  }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get transactions", "id": "2cd72c73-ae93-4d3f-af65-d581e2d59095", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query ($search: TransactionSearchInput!) {\r\n    transactions(search: $search) {\r\n        edges {\r\n            node {\r\n                __typename\r\n                ... on ClientCashTrade {\r\n                    uuid\r\n                    updatedAt\r\n                    extTransactionId\r\n                    fee\r\n                    feeCurrency\r\n                    description\r\n                    quantity\r\n                    price\r\n                    currency\r\n                    intOrderId\r\n                    extOrderId\r\n                    orderId\r\n                    baseCurrency\r\n                    portfolio\r\n                    counterPortfolio\r\n                }\r\n                ... on StreetCashTrade {\r\n                    uuid\r\n                    updatedAt\r\n                    extTransactionId\r\n                    fee\r\n                    feeCurrency\r\n                    description\r\n                    quantity\r\n                    price\r\n                    currency\r\n                    intOrderId\r\n                    extOrderId\r\n                    orderId\r\n                    baseCurrency\r\n                    portfolio\r\n                    venueAccount\r\n                }\r\n                ... on ClientAssetTrade {\r\n                    uuid\r\n                    updatedAt\r\n                    extTransactionId\r\n                    fee\r\n                    feeCurrency\r\n                    description\r\n                    quantity\r\n                    price\r\n                    currency\r\n                    intOrderId\r\n                    extOrderId\r\n                    orderId\r\n                    security\r\n                    portfolio\r\n                    counterPortfolio\r\n                }\r\n                ... on StreetAssetTrade {\r\n                    uuid\r\n                    updatedAt\r\n                    extTransactionId\r\n                    fee\r\n                    feeCurrency\r\n                    description\r\n                    quantity\r\n                    price\r\n                    currency\r\n                    intOrderId\r\n                    extOrderId\r\n                    orderId\r\n                    security\r\n                    portfolio\r\n                    venueAccount\r\n                }\r\n            }\r\n            cursor\r\n        },\r\n        pageInfo {\r\n            hasNextPage,\r\n            endCursor\r\n        }\r\n    }\r\n}", "variables": "{\r\n    \"search\" : {\r\n    }\r\n    \r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get transaction types", "id": "2d2b69e5-c12a-468e-9db3-8e07218c00f8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n    transactionTypes\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get user permissions", "id": "3da5a2af-3e82-44f0-a935-3964b3f506b8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n  userWithPermissions {\r\n    username\r\n    groups {\r\n        name\r\n        permissions {\r\n            resource\r\n            scope\r\n            resourceId\r\n        }\r\n    }\r\n    permissions {\r\n        resource\r\n        scope\r\n        resourceId\r\n    }\r\n  }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get user permissions for resource", "id": "e93ea874-a1f8-4443-ad40-970c92773e8d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n  usersPermissionsForResource(resource: VENUE_ACCOUNT, resourceId: \"bitmex-testnet1\") {\r\n    username\r\n    scopes\r\n  }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get group permissions for resource", "id": "882c07af-beb5-4fce-89dd-47b481f3ba1e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n  groupsPermissionsForResource(resource: VENUE_ACCOUNT, resourceId: \"simulator\") {\r\n    groupName\r\n    scopes\r\n  }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get venue accounts", "id": "665224b4-c5d3-41b3-9b24-b33f25988f98", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n    venueAccounts {\r\n        venue\r\n        venueAccounts\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get venue names (CSS)", "id": "cf375cc1-f295-47d6-8e5d-8b2f42f21131", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "{\r\n    clientSideSecuritiesVenueNames\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update security", "id": "49e3aa53-99e5-4575-93df-ec6a6e53e959", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateSecurity($request: UpdateSecurityInput) {\r\n\tupdateSecurity(request: $request) {\r\n        status\r\n  }  \r\n}", "variables": "{\r\n  \"request\": {\r\n                \"baseSecurity\": {\r\n                    \"inverseContract\": false\r\n                },\r\n                \"tradingConstraints\": {\r\n                    \"minQty\": \"0,00001\",\r\n                    \"maxQty\": \"*********\",\r\n                    \"priceScale\" : \"5\",\r\n                    \"qtyScale\": \"5\",\r\n                    \"qtyIncr\": \"0,00001\",\r\n                    \"tradeable\": true\r\n                },\r\n                \"securityIdentifiers\": {\r\n                },\r\n                \"symbol\": \"BASEUSDT@FOREX@Bank\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create venue account", "id": "a5cff457-63f7-4984-8d17-51ece781070f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation ($request: CreateVenueAccountInput!) {\r\n    createVenueAccount(request: $request) {\r\n        status\r\n    }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"venueName\": \"WydenMock\",\r\n        \"venueAccountName\": \"e2e_mock2\"\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get user data", "id": "e24f6b13-d23a-4b26-8217-29af90c15b85", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJHeXI0cjU0UVFoTW01WmpyUm9CaDMza3UtZjg3Vm54d21jRFFDZmF3MXhRIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PsPu79V-iVt5CFUEtFnUrGRZ6s_j8mMOEjuYvtghQHAFszPmbv4c5d_yvRneo18rfafgKwtYEFHl75jtVyLqurSXJ3BmdsREf4DRJXzHRkreov7TH9Cqf6srjovlIsIzfKE56jh8iYrHS-q87w-95NTlRLDr9ipnYhYw2fyh7WFEDgKCeqnTreYrzCnO3BQVXfg_4DyXrfzAumzqkb13W7zJkL9BJmvB_qjpHeBx7w0yxk2QQOryMNhpkNXBMktIV2yMhxIgXU34eI_70NpucJ9YxZkWSnTRFgmlL_3MSQCcK1s7xOSnbPkyTHOupTw6iZIciWR_4qO1fGtxeAJLFA", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query UserData {\r\n    userData {\r\n        data\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update user data", "id": "fe6947a1-cfe1-41a4-8fbe-ccf84f44e935", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJHeXI0cjU0UVFoTW01WmpyUm9CaDMza3UtZjg3Vm54d21jRFFDZmF3MXhRIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PsPu79V-iVt5CFUEtFnUrGRZ6s_j8mMOEjuYvtghQHAFszPmbv4c5d_yvRneo18rfafgKwtYEFHl75jtVyLqurSXJ3BmdsREf4DRJXzHRkreov7TH9Cqf6srjovlIsIzfKE56jh8iYrHS-q87w-95NTlRLDr9ipnYhYw2fyh7WFEDgKCeqnTreYrzCnO3BQVXfg_4DyXrfzAumzqkb13W7zJkL9BJmvB_qjpHeBx7w0yxk2QQOryMNhpkNXBMktIV2yMhxIgXU34eI_70NpucJ9YxZkWSnTRFgmlL_3MSQCcK1s7xOSnbPkyTHOupTw6iZIciWR_4qO1fGtxeAJLFA", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query UserData {\r\n    userData {\r\n        data\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Send order", "id": "6053596c-55ce-4eb8-a084-b9b31d8af3b1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation SendOrder($request: NewOrderSingleRequestInput!) {\r\n  sendOrder(request: $request) {\r\n    clientId\r\n    __typename\r\n  }\r\n}", "variables": "{\r\n  \"request\": {\r\n    \"clientId\": \"admin\",\r\n    \"clOrderId\": \"admin-01\",\r\n    \"symbol\": \"BTCUSD@FOREX@Simulator\",\r\n    \"side\": \"BUY\",\r\n    \"orderType\": \"MARKET\",\r\n    \"quantity\": 1,\r\n    \"price\": null,\r\n    \"tif\": \"DAY\",\r\n    \"targetVenueAccount\": \"simulator\",\r\n    \"portfolioId\": \"t\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get portfolios", "id": "0bbdc834-84c2-4d5f-a05a-540560590a31", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n    portfolios {\r\n        id\r\n        name\r\n        createdAt\r\n        portfolioCurrency\r\n        tags {\r\n            key\r\n            value\r\n        }\r\n        archivedAt\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create portfolio", "id": "97ff3ebd-380f-4304-b1dd-392084773a8a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation ($request: CreatePortfolioInput!) {\r\n    createPortfolio(request: $request) {\r\n        status\r\n    }\r\n}", "variables": "{\r\n    \"request\" : {\r\n        \"name\" : \"portfolio-trader1\",\r\n        \"portfolioCurrency\" : \"USD\",\r\n        \"tags\" : []\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update portfolio", "id": "7496f0b6-c173-4b95-aa19-2a455ab9f0d9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation ($request: UpdatePortfolioInput!) {\r\n    updatePortfolio(request: $request) {\r\n        status\r\n    }\r\n}", "variables": "{\r\n    \"request\" : {\r\n        \"id\" : \"7bddb43f-6dd0-4bcd-bc36-3775e028c321\",\r\n        \"name\" : \"portfolio2\",\r\n        \"tags\" : [],\r\n        \"archived\" : \"true\"\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Request order status", "id": "6b228cab-04a9-4502-88ed-2e5c63e34916", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation ($request: OrderStatusRequestInput!) {\r\n                   orderStatusRequest(request: $request) {\r\n                     clientId\r\n                   }\r\n                 }", "variables": "{\r\n    \"request\": {\r\n        \"orderId\": \"d80a64b7-b077-4226-ae53-93d1ea390ce0\",\r\n        \"clOrderId\": \"0a71a821-d631-40c9-bc15-4fca5e4036df\",\r\n        \"clientId\": \"admin\"\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Health check", "id": "60b55e57-6171-4635-984f-86c61a790b8c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{rest.api.url}}/actuator/health/readinessState", "host": ["{{rest.api.url}}"], "path": ["actuator", "health", "readinessState"]}}, "response": []}, {"name": "Get positions", "id": "0838004a-feb3-4d04-b41f-576352a1c6b5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query ($search: AccountingSearchInput!) {\r\n    positions(search: $search) {\r\n        edges {\r\n            node {\r\n                updatedAt\r\n                quantity\r\n                bookingCurrency\r\n                netRealizedPnl\r\n                grossRealizedPnl\r\n                netCost\r\n                grossCost\r\n                reference\r\n                instrument\r\n                currency\r\n                security\r\n                portfolio\r\n                account\r\n                notionalQuantity\r\n                marketValue\r\n                marketValuePc\r\n                netRealizedPnlPc\r\n                netCostPc\r\n                grossRealizedPnlPc\r\n                grossCostPc\r\n                netAveragePrice\r\n                grossAveragePrice\r\n                netUnrealizedPnl\r\n                grossUnrealizedPnl\r\n                netUnrealizedPnl\r\n                grossUnrealizedPnlPc\r\n            }\r\n            cursor\r\n        },\r\n        pageInfo {\r\n            hasNextPage,\r\n            endCursor\r\n        }\r\n    \r\n    }\r\n}", "variables": "{\r\n    \"search\" : {\r\n        \"first\": 50,\r\n        \"portfolio\": \"Portfolio2\"\r\n    }\r\n    \r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get orders", "id": "b8645fee-fdc8-4ab6-a303-eaee2737e596", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query  {\r\n    orderStates(portfolioId: \"Trader_Portfolio\", symbol: \"BTCUSDT@FOREX@BitMEX\") {\r\n        clOrderId\r\n        clientId\r\n        orderId\r\n        orderQty\r\n        orderStatus\r\n        origClOrderId\r\n        reason\r\n        symbol\r\n        side\r\n        venueTicker\r\n        venueTimestamp\r\n        createdAt\r\n        updatedAt\r\n        stopPrice\r\n        limitPrice\r\n        filledQty\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Subscribe order states with snapshot", "id": "0253e4a2-2a3c-4fe3-b1e5-3d1c823189f6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "subscription OrderStatesWithSnapshot {\r\n    orderStatesWithSnapshot(portfolioId: null, venueAccount: null, isOpen: null, historyCap: null) {\r\n        orderStatus\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get broker desk config", "id": "691ab066-ba82-402c-8c28-eefaa23b1da1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n    tradingEngineConfig {\r\n        instrumentId\r\n        lowThreshold\r\n        highThreshold\r\n        targetExposure\r\n        markupPercentage\r\n        counterPortfolioId\r\n        streetSideReferenceInstrument\r\n        enabled\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create broker desk config", "id": "a60029c9-2ec8-4425-b0f7-ff4e46cf94de", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation ($request: TradingEngineConfigInput!) {\r\n    createTradingEngine(request: $request) {\r\n        clientId\r\n    }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"instrumentId\": \"BTCUSDT@FOREX@Bank\",\r\n        \"lowThreshold\": -10,\r\n        \"highThreshold\": 10,\r\n        \"targetExposure\": 0,\r\n        \"markupPercentage\": 5,\r\n        \"counterPortfolioId\": \"BANK_Portfolio\",\r\n        \"streetSideReferenceInstrument\": \"BTCUSDT@FOREX@Simulator\",\r\n        \"enabled\": true\r\n\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create autohedger", "id": "********-f408-4956-b652-a6290d2fec91", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation ($request: AutoHedgerConfigInput!) {\r\n    createAutoHedger(request: $request) {\r\n        clientId\r\n    }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"asset\": \"BTC\",\r\n        \"lowThreshold\": -5,\r\n        \"highThreshold\": 5,\r\n        \"targetExposure\": 0,\r\n        \"portfolioId\": \"BANK_Portfolio\",\r\n        \"streetSideHedgeInstrument\": \"BTCUSDT@FOREX@Simulator\",\r\n        \"enabled\": true\r\n\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get log level", "id": "b5b405eb-5213-47a1-904a-8c0415fe7d2a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.api.url}}/actuator/loggers/io.wyden", "host": ["{{rest.api.url}}"], "path": ["actuator", "loggers", "io.wyden"]}}, "response": []}, {"name": "Update log level", "id": "6f83a788-89ff-4ab4-8985-14c31f9258ab", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"configuredLevel\": \"INFO\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{rest.api.url}}/actuator/loggers/io.wyden", "host": ["{{rest.api.url}}"], "path": ["actuator", "loggers", "io.wyden"]}}, "response": []}, {"name": "Get venues", "id": "1115ca88-cafa-4ec3-ab80-7941fb9f119a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Auth}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n    venues(venueType: STREET) {\r\n        name\r\n        venueType\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}]}